"""
Compatibility layer for lightweight_chart module.

This module provides backward compatibility for code that still imports from
the old 'lightweight_chart' module name. All functionality has been moved to
the 'vue_echarts' module to reflect the completed migration from TradingView
Lightweight Charts to Vue.js + ECharts.

DEPRECATED: Please update your imports to use 'vue_echarts' instead.
This compatibility layer will be removed in a future version.

Migration guide:
- OLD: from user_scripts_restructured.visualization.lightweight_chart import X
- NEW: from user_scripts_restructured.visualization.vue_echarts import X
"""

import warnings
from pathlib import Path

# Issue deprecation warning
warnings.warn(
    "The 'lightweight_chart' module is deprecated. "
    "Please update your imports to use 'vue_echarts' instead. "
    "This module will be removed in a future version.",
    DeprecationWarning,
    stacklevel=2
)

# Re-export core APIs from vue_echarts
try:
    # Import only the essential core components to avoid circular imports
    from ..vue_echarts.config import ChartConfig, ChartConfigManager
    from ..vue_echarts.app import ChartServer
    
    # Export essential classes
    __all__ = [
        'ChartConfig',
        'ChartConfigManager', 
        'ChartServer',
    ]
    
except ImportError as e:
    # Fallback error message if vue_echarts module is not found
    raise ImportError(
        f"Failed to import from vue_echarts module: {e}. "
        "Please ensure the vue_echarts module is properly installed and accessible."
    ) from e

# Module metadata
__version__ = "4.0.0-compat"
__author__ = "Nautilus Trader Visualization Team"
__description__ = "Compatibility layer for legacy lightweight_chart imports"