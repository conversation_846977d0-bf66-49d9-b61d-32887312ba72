"""
Compatibility module for lightweight_chart.data_loader.

DEPRECATED: Please update your imports to use 'vue_echarts.data_loader' instead.
This module will be removed in a future version.
"""

import warnings

# Issue deprecation warning
warnings.warn(
    "The 'lightweight_chart.data_loader' module is deprecated. "
    "Please update your imports to use 'vue_echarts.data_loader' instead. "
    "This module will be removed in a future version.",
    DeprecationWarning,
    stacklevel=2
)

# Re-export from vue_echarts.data_loader
from ..vue_echarts.data_loader import *

__all__ = ['ChartDataLoader', 'load_instrument_data', 'get_available_instruments', 'set_catalog_path']