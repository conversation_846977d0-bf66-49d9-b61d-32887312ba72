import {
  ChartOptions,
  CandlestickSeriesOptions,
  VolumeSeriesOptions,
  ChartInstance,
  SeriesInstance,
  VolumeSeriesInstance,
  OHLCData,
  VolumeData,
  ChartResponse
} from '../types';



export class ChartManager {
  private chart: ChartInstance | null = null;
  private volumeChart: ChartInstance | null = null;
  private candlestickSeries: SeriesInstance | null = null;
  private volumeSeries: VolumeSeriesInstance | null = null;
  private isUpdatingSync = false;

  constructor(
    private chartContainer: HTMLElement,
    private volumeContainer: HTMLElement,
    private instrumentId: string
  ) {}

  initialize(): void {
    this.createCharts();
    this.createSeries();
    this.setupSynchronization();
    this.setupResize();
  }

  private createCharts(): void {
    const chartOptions = this.getChartOptions();
    const volumeChartOptions = this.getVolumeChartOptions();

    this.chart = createChart(this.chartContainer, chartOptions);
    this.volumeChart = createChart(this.volumeContainer, volumeChartOptions);
  }

  private createSeries(): void {
    if (!this.chart || !this.volumeChart) {
      throw new Error('Charts must be created before series');
    }

    const candlestickOptions = this.getCandlestickOptions();
    const volumeOptions = this.getVolumeOptions();

    this.candlestickSeries = this.chart.addSeries(CandlestickSeries, candlestickOptions);
    this.volumeSeries = this.volumeChart.addSeries(HistogramSeries, volumeOptions);
  }

  private setupSynchronization(): void {
    if (!this.chart || !this.volumeChart) return;

    // Price chart drives volume chart
    this.chart.timeScale().subscribeVisibleTimeRangeChange((timeRange: { from: Time; to: Time } | null) => {
      if (this.isUpdatingSync) return;
      try {
        if (timeRange &&
            timeRange.from !== null && timeRange.from !== undefined &&
            timeRange.to !== null && timeRange.to !== undefined &&
            this.volumeChart && this.volumeChart.timeScale) {
          this.isUpdatingSync = true;
          this.volumeChart.timeScale().setVisibleRange(timeRange);
          setTimeout(() => { this.isUpdatingSync = false; }, 10);
        }
      } catch (error: unknown) {
        this.isUpdatingSync = false;
        const errorMessage = error instanceof Error ? error.message : String(error);
        if (errorMessage !== "Value is null") {
          console.log('Chart sync warning (time range):', errorMessage);
        }
      }
    });

    this.chart.timeScale().subscribeVisibleLogicalRangeChange((range: LogicalRange | null) => {
      if (this.isUpdatingSync) return;
      try {
        if (range &&
            range.from !== null && range.from !== undefined &&
            range.to !== null && range.to !== undefined &&
            this.volumeChart && this.volumeChart.timeScale) {
          this.isUpdatingSync = true;
          this.volumeChart.timeScale().setVisibleLogicalRange(range);
          setTimeout(() => { this.isUpdatingSync = false; }, 10);
        }
      } catch (error: unknown) {
        this.isUpdatingSync = false;
        const errorMessage = error instanceof Error ? error.message : String(error);
        if (errorMessage !== "Value is null") {
          console.log('Chart sync warning (logical range):', errorMessage);
        }
      }
    });
  }

  private setupResize(): void {
    window.addEventListener('resize', () => {
      if (this.chart && this.volumeChart) {
        this.chart.applyOptions({
          width: this.chartContainer.clientWidth,
          height: this.chartContainer.clientHeight
        });

        this.volumeChart.applyOptions({
          width: this.volumeContainer.clientWidth,
          height: this.volumeContainer.clientHeight
        });
      }
    });
  }

  updateData(data: ChartResponse): void {
    if (!this.candlestickSeries || !this.volumeSeries) {
      throw new Error('Series must be created before updating data');
    }

    // Validate and set OHLC data
    const validOhlcData = this.validateOhlcData(data.ohlc);
    if (validOhlcData.length === 0) {
      throw new Error('No valid OHLC data points found');
    }

    this.candlestickSeries.setData(validOhlcData);

    // Set volume data with colors
    if (data.volume && Array.isArray(data.volume) && data.volume.length > 0) {
      const validVolumeData = this.validateVolumeData(data.volume);
      if (validVolumeData.length > 0 && validOhlcData.length === validVolumeData.length) {
        const volumeDataWithColors = this.addVolumeColors(validVolumeData, validOhlcData);
        this.volumeSeries.setData(volumeDataWithColors);
      } else if (validVolumeData.length > 0) {
        this.volumeSeries.setData(validVolumeData);
      }
    }

    // Fit content after data is set
    setTimeout(() => {
      if (validOhlcData.length > 0 && this.chart && this.volumeChart) {
        this.chart.timeScale().fitContent();
        this.volumeChart.timeScale().fitContent();
      }
    }, 100);
  }

  updateSingleBar(ohlc: OHLCData, volume: VolumeData): void {
    if (!this.candlestickSeries || !this.volumeSeries) return;

    this.candlestickSeries.update(ohlc);
    
    const volumeWithColor = {
      ...volume,
      color: ohlc.close >= ohlc.open ? '#26a69a' : '#ef5350'
    };
    this.volumeSeries.update(volumeWithColor);
  }

  private validateOhlcData(data: OHLCData[]): OHLCData[] {
    return data.filter(item => {
      return item &&
             typeof item.time === 'number' &&
             typeof item.open === 'number' &&
             typeof item.high === 'number' &&
             typeof item.low === 'number' &&
             typeof item.close === 'number' &&
             !isNaN(item.time) &&
             !isNaN(item.open) &&
             !isNaN(item.high) &&
             !isNaN(item.low) &&
             !isNaN(item.close);
    });
  }

  private validateVolumeData(data: VolumeData[]): VolumeData[] {
    return data.filter(item => {
      return item &&
             typeof item.time === 'number' &&
             typeof item.value === 'number' &&
             !isNaN(item.time) &&
             !isNaN(item.value);
    });
  }

  private addVolumeColors(volumeData: VolumeData[], ohlcData: OHLCData[]): VolumeData[] {
    return volumeData.map((item, index) => {
      const ohlcBar = ohlcData[index];
      let barIsUp: boolean;
      
      if (ohlcBar.close > ohlcBar.open) {
        barIsUp = true;
      } else if (ohlcBar.close < ohlcBar.open) {
        barIsUp = false;
      } else {
        if (index > 0 && ohlcData[index - 1]) {
          barIsUp = ohlcBar.close >= ohlcData[index - 1].close;
        } else {
          barIsUp = true;
        }
      }
      
      return {
        ...item,
        color: barIsUp ? '#26a69a' : '#ef5350'
      };
    });
  }

  private getChartOptions(): ChartOptions {
    return {
      layout: {
        background: { type: ColorType.Solid, color: '#ffffff' },
        textColor: '#191919',
      },
      grid: {
        vertLines: { color: 'rgba(197, 203, 206, 0.5)' },
        horzLines: { color: 'rgba(197, 203, 206, 0.5)' }
      },
      timeScale: {
        timeVisible: true,
        secondsVisible: false,
        borderColor: 'rgba(197, 203, 206, 0.8)',
      },
      crosshair: {
        mode: CrosshairMode.Normal,
        vertLine: {
          color: '#758696',
          width: 1 as LineWidth,
          style: LineStyle.Dashed,
        },
        horzLine: {
          color: '#758696',
          width: 1 as LineWidth,
          style: LineStyle.Dashed,
        },
      },
      localization: {
        locale: 'en-US',
        dateFormat: 'dd/MM/yyyy',
      },
    };
  }

  private getVolumeChartOptions() {
    return {
      height: 150,
      layout: {
        background: { type: ColorType.Solid, color: '#ffffff' },
        textColor: '#191919',
      },
      grid: {
        vertLines: { color: 'rgba(197, 203, 206, 0.5)' },
        horzLines: { color: 'rgba(197, 203, 206, 0.5)' }
      },
      timeScale: {
        visible: false,
      },
      rightPriceScale: {
        scaleMargins: {
          top: 0.1,
          bottom: 0.1,
        },
      },
      localization: {
        locale: 'en-US',
        dateFormat: 'dd/MM/yyyy',
      },
    };
  }

  private getCandlestickOptions(): CandlestickSeriesOptions {
    return {
      upColor: '#26a69a',
      downColor: '#ef5350',
      borderVisible: false,
      wickUpColor: '#26a69a',
      wickDownColor: '#ef5350',
      priceFormat: {
        type: 'price',
        precision: 2,
        minMove: 0.01,
      },
      lastValueVisible: true,
      priceLineVisible: true,
      priceLineWidth: 1,
      priceLineColor: '#5294ff',
      priceLineStyle: LineStyle.Dashed,
    };
  }

  private getVolumeOptions(): VolumeSeriesOptions {
    return {
      color: '#26a69a',
      priceFormat: {
        type: 'volume',
        precision: 0,
      },
      priceScaleId: '',
    };
  }

  destroy(): void {
    if (this.chart) {
      this.chart.remove();
      this.chart = null;
    }
    if (this.volumeChart) {
      this.volumeChart.remove();
      this.volumeChart = null;
    }
    this.candlestickSeries = null;
    this.volumeSeries = null;
  }
}