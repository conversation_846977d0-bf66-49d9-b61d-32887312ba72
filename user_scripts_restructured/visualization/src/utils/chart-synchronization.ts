// Chart synchronization manager for race condition prevention

import type {
  ChartSyncState,
  SyncOperation,
  SyncLock,
  ChartSyncConfiguration,
  SyncR<PERSON>ult,
  Sync<PERSON>rror,
  ChartPair,
  ChartSyncEvents,
  ChartSyncEventHandler,
} from '../types/chart-sync';



/**
 * Mutex implementation for exclusive chart synchronization
 */
class AsyncMutex {
  private locked = false;
  private waitQueue: Array<() => void> = [];

  async runExclusive<T>(fn: () => Promise<T> | T): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      const execute = async () => {
        try {
          this.locked = true;
          const result = await fn();
          resolve(result);
        } catch (error) {
          reject(error);
        } finally {
          this.locked = false;
          this.processQueue();
        }
      };

      if (this.locked) {
        this.waitQueue.push(execute);
      } else {
        execute();
      }
    });
  }

  private processQueue(): void {
    if (this.waitQueue.length > 0 && !this.locked) {
      const next = this.waitQueue.shift();
      if (next) {
        next();
      }
    }
  }

  isLocked(): boolean {
    return this.locked;
  }

  getQueueLength(): number {
    return this.waitQueue.length;
  }
}

/**
 * Chart synchronization manager to prevent race conditions
 */
export class ChartSynchronizationManager {
  private syncMutex = new AsyncMutex();
  private state: ChartSyncState;
  private config: ChartSyncConfiguration;
  private eventHandlers: Partial<ChartSyncEvents> = {};
  private operationTimeouts: Map<string, NodeJS.Timeout> = new Map();

  constructor(config: Partial<ChartSyncConfiguration> = {}) {
    this.config = {
      maxConcurrentOperations: 1,
      operationTimeoutMs: 5000,
      maxRetries: 3,
      debounceDelayMs: 50,
      enableDebugging: false,
      ...config,
    };

    this.state = {
      isUpdating: false,
      lastUpdate: 0,
      pendingOperations: new Set(),
      operationQueue: [],
      locks: new Map(),
    };
  }

  /**
   * Synchronize charts with atomic operations
   */
  async synchronizeCharts(
    operation: SyncOperation,
    charts: ChartPair
  ): Promise<SyncResult> {
    const startTime = Date.now();

    try {
      this.validateOperation(operation);
      this.addOperation(operation);

      const result = await this.syncMutex.runExclusive(async () => {
        return this.executeSync(operation, charts);
      });

      const syncResult: SyncResult = {
        success: true,
        operationId: operation.id,
        duration: Date.now() - startTime,
        metadata: result,
      };

      this.emitEvent('sync:complete', syncResult);
      return syncResult;

    } catch (error) {
      const syncError: SyncError = {
        code: this.classifyError(error),
        message: error instanceof Error ? error.message : String(error),
        operationId: operation.id,
        timestamp: Date.now(),
        recoverable: this.isRecoverableError(error),
        context: { operation, charts: this.getChartInfo(charts) },
      };

      this.emitEvent('sync:error', syncError);

      return {
        success: false,
        operationId: operation.id,
        duration: Date.now() - startTime,
        error: syncError,
      };
    } finally {
      this.removeOperation(operation);
    }
  }

  /**
   * Queue operation for execution
   */
  queueOperation(operation: SyncOperation): void {
    this.state.operationQueue.push(operation);
    this.emitEvent('queue:updated', [...this.state.operationQueue]);

    if (this.config.enableDebugging) {
      console.log(`Queued operation: ${operation.type} (${operation.id})`);
    }
  }

  /**
   * Recover from sync error
   */
  async recoverFromSyncError(error: SyncError): Promise<boolean> {
    if (!error.recoverable) {
      return false;
    }

    try {
      // Clear any existing locks
      this.clearLocksForOperation(error.operationId);

      // Reset sync state
      this.state.isUpdating = false;

      // Retry the operation if within retry limits
      const operation = this.findOperationById(error.operationId);
      if (operation && (operation.retryCount || 0) < this.config.maxRetries) {
        operation.retryCount = (operation.retryCount || 0) + 1;
        this.emitEvent('sync:retry', operation);
        return true;
      }

      return false;
    } catch (recoveryError) {
      if (this.config.enableDebugging) {
        console.error('Recovery failed:', recoveryError);
      }
      return false;
    }
  }

  /**
   * Add event handler
   */
  on<K extends keyof ChartSyncEvents>(
    event: K,
    handler: ChartSyncEvents[K]
  ): void {
    this.eventHandlers[event] = handler;
  }

  /**
   * Remove event handler
   */
  off<K extends keyof ChartSyncEvents>(event: K): void {
    delete this.eventHandlers[event];
  }

  /**
   * Get current sync state
   */
  getState(): Readonly<ChartSyncState> {
    return { ...this.state };
  }

  /**
   * Check if currently synchronizing
   */
  isSynchronizing(): boolean {
    return this.state.isUpdating || this.syncMutex.isLocked();
  }

  /**
   * Clean up resources
   */
  destroy(): void {
    // Clear all timeouts
    this.operationTimeouts.forEach(timeout => clearTimeout(timeout));
    this.operationTimeouts.clear();

    // Clear all locks
    this.state.locks.clear();

    // Clear pending operations
    this.state.pendingOperations.clear();
    this.state.operationQueue = [];

    // Clear event handlers
    this.eventHandlers = {};
  }

  // Private methods

  private validateOperation(operation: SyncOperation): void {
    if (!operation.id || !operation.type) {
      throw new Error('Invalid operation: missing id or type');
    }

    if (this.state.pendingOperations.has(operation)) {
      throw new Error(`Operation ${operation.id} already pending`);
    }
  }

  private addOperation(operation: SyncOperation): void {
    this.state.pendingOperations.add(operation);
    this.state.lastUpdate = Date.now();

    // Set timeout for operation
    const timeout = setTimeout(() => {
      this.handleOperationTimeout(operation);
    }, this.config.operationTimeoutMs);

    this.operationTimeouts.set(operation.id, timeout);
    this.emitEvent('sync:start', operation);
  }

  private removeOperation(operation: SyncOperation): void {
    this.state.pendingOperations.delete(operation);

    const timeout = this.operationTimeouts.get(operation.id);
    if (timeout) {
      clearTimeout(timeout);
      this.operationTimeouts.delete(operation.id);
    }
  }

  private async executeSync(
    operation: SyncOperation,
    charts: ChartPair
  ): Promise<Record<string, unknown>> {
    this.state.isUpdating = true;

    try {
      switch (operation.type) {
        case 'timeRange':
          return this.syncTimeRange(operation, charts);
        case 'logicalRange':
          return this.syncLogicalRange(operation, charts);
        case 'data':
          return this.syncData(operation, charts);
        case 'initialization':
          return this.syncInitialization(operation, charts);
        case 'cleanup':
          return this.syncCleanup(operation, charts);
        default:
          throw new Error(`Unknown operation type: ${operation.type}`);
      }
    } finally {
      this.state.isUpdating = false;
    }
  }

  private syncTimeRange(
    operation: SyncOperation,
    charts: ChartPair
  ): Record<string, unknown> {
    const timeRange = operation.metadata?.timeRange as { from: Time; to: Time } | null;
    
    if (!timeRange) {
      throw new Error('Time range data missing from operation');
    }

    // Sync volume chart to price chart time range
    if (charts.volumeChart?.chart && charts.volumeChart.chart.timeScale) {
      charts.volumeChart.chart.timeScale().setVisibleRange(timeRange);
    }

    return { timeRange, synchronized: true };
  }

  private syncLogicalRange(
    operation: SyncOperation,
    charts: ChartPair
  ): Record<string, unknown> {
    const logicalRange = operation.metadata?.logicalRange as LogicalRange | null;
    
    if (!logicalRange) {
      throw new Error('Logical range data missing from operation');
    }

    // Sync volume chart to price chart logical range
    if (charts.volumeChart?.chart && charts.volumeChart.chart.timeScale) {
      charts.volumeChart.chart.timeScale().setVisibleLogicalRange(logicalRange);
    }

    return { logicalRange, synchronized: true };
  }

  private syncData(
    operation: SyncOperation,
    charts: ChartPair
  ): Record<string, unknown> {
    // Data synchronization logic would go here
    // This is a placeholder for data-specific sync operations
    return { dataSynced: true };
  }

  private syncInitialization(
    operation: SyncOperation,
    charts: ChartPair
  ): Record<string, unknown> {
    // Initialization synchronization logic
    return { initialized: true };
  }

  private syncCleanup(
    operation: SyncOperation,
    charts: ChartPair
  ): Record<string, unknown> {
    // Cleanup synchronization logic
    return { cleanedUp: true };
  }

  private classifyError(error: unknown): SyncError['code'] {
    if (error instanceof Error) {
      if (error.message.includes('timeout')) return 'TIMEOUT';
      if (error.message.includes('deadlock')) return 'DEADLOCK';
      if (error.message.includes('conflict')) return 'CONFLICT';
      if (error.message.includes('invalid')) return 'INVALID_STATE';
    }
    return 'UNKNOWN';
  }

  private isRecoverableError(error: unknown): boolean {
    const code = this.classifyError(error);
    return ['TIMEOUT', 'CONFLICT'].includes(code);
  }

  private handleOperationTimeout(operation: SyncOperation): void {
    this.emitEvent('sync:timeout', operation);
    this.removeOperation(operation);

    if (this.config.enableDebugging) {
      console.warn(`Operation timeout: ${operation.type} (${operation.id})`);
    }
  }

  private clearLocksForOperation(operationId: string): void {
    const locksToRemove = Array.from(this.state.locks.entries())
      .filter(([, lock]) => lock.operationId === operationId)
      .map(([id]) => id);

    locksToRemove.forEach(lockId => {
      this.state.locks.delete(lockId);
    });
  }

  private findOperationById(operationId: string): SyncOperation | undefined {
    return Array.from(this.state.pendingOperations)
      .find(op => op.id === operationId);
  }

  private getChartInfo(charts: ChartPair): Record<string, unknown> {
    return {
      priceChart: {
        id: charts.priceChart.id,
        type: charts.priceChart.type,
        isReady: charts.priceChart.isReady,
      },
      volumeChart: {
        id: charts.volumeChart.id,
        type: charts.volumeChart.type,
        isReady: charts.volumeChart.isReady,
      },
    };
  }

  private emitEvent<K extends keyof ChartSyncEvents>(
    event: K,
    data: Parameters<ChartSyncEvents[K]>[0]
  ): void {
    const handler = this.eventHandlers[event];
    if (handler && typeof handler === 'function') {
      try {
        (handler as any)(data);
      } catch (error) {
        if (this.config.enableDebugging) {
          console.error(`Error in sync event handler ${String(event)}:`, error);
        }
      }
    }
  }
}

// Utility function to create sync operations
export function createSyncOperation(
  type: SyncOperation['type'],
  metadata?: Record<string, unknown>,
  priority: SyncOperation['priority'] = 'medium'
): SyncOperation {
  return {
    id: `sync_${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    type,
    timestamp: Date.now(),
    completed: false,
    priority,
    metadata,
    retryCount: 0,
    maxRetries: 3,
  };
}

// Default export
export default ChartSynchronizationManager;