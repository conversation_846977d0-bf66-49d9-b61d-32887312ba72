import {
  InfiniteScrollConfig,
  DEFAULT_INFINITE_SCROLL_CONFIG,
  DataRange,
  LoadDirection,
  LoadingState,
  DataChunk,
  MemoryStats,
  DataManagerState,
  InfiniteScrollEvents,
  ScrollBoundary,
  LoadRequest,
  InfiniteScrollMetrics,
} from '../types/infinite-scroll';
import {
  OHLCData,
  VolumeData,
  Timeframe,
  ChartResponse,
} from '../types/chart';
import { LogicalRange } from '../types';
import { apiClient } from './api-client';

import type { Socket } from 'socket.io-client';
import { ChartDataRequest, ChartDataResponse } from '../types/websocket';

/**
 * Manages data loading and memory for infinite scrolling charts
 */
export class InfiniteScrollDataManager {
  private config: InfiniteScrollConfig;
  private state: DataManagerState;
  private events: Partial<InfiniteScrollEvents>;
  private metrics: InfiniteScrollMetrics;
  private loadRequestCounter = 0;

  constructor(
    instrument: string,
    timeframe: Timeframe,
    config: Partial<InfiniteScrollConfig> = {},
    events: Partial<InfiniteScrollEvents> = {},
    private socket?: Socket
  ) {
    this.config = { ...DEFAULT_INFINITE_SCROLL_CONFIG, ...config };
    this.events = events;
    
    this.state = {
      instrument,
      timeframe,
      loadedRanges: [],
      loadingStates: new Map(),
      allOhlcData: [],
      allVolumeData: [],
      memoryStats: this.createEmptyMemoryStats(),
      initialized: false,
      pendingRequests: new Set(),
    };

    this.metrics = {
      totalLoadRequests: 0,
      successfulLoads: 0,
      failedLoads: 0,
      averageLoadTimeMs: 0,
      totalBarsLoaded: 0,
      totalMemoryTrimmed: 0,
      currentMemoryUsageMB: 0,
      cacheHitRate: 0,
    };
  }

  /**
   * Initialize with initial data
   */
  async initialize(initialData: ChartResponse): Promise<void> {
    this.state.allOhlcData = [...initialData.ohlc];
    this.state.allVolumeData = [...initialData.volume];
    
    if (initialData.ohlc.length > 0) {
      const range: DataRange = {
        startTime: this.getTimeAsSeconds(initialData.ohlc[0].time),
        endTime: this.getTimeAsSeconds(initialData.ohlc[initialData.ohlc.length - 1].time),
        barCount: initialData.ohlc.length,
        loading: false,
      };
      this.state.loadedRanges = [range];
    }

    this.updateMemoryStats();
    this.state.initialized = true;
    
    this.emitEvent('onStatusUpdated', `Initialized with ${initialData.ohlc.length} bars`);
  }

  /**
   * Check if we need to load more data based on logical range
   */
  checkAndLoadData(logicalRange: LogicalRange): void {
    if (!this.state.initialized) {
      console.log('[InfiniteScroll] Data manager not initialized, skipping load check');
      return;
    }

    this.state.lastLogicalRange = logicalRange;
    const boundary = this.detectScrollBoundary(logicalRange);

    // Debug logging
    console.log(`[InfiniteScroll] Scroll check - Left: ${boundary.leftDistance}, Right: ${boundary.rightDistance}, Trigger: ${this.config.loadTriggerDistance}`);
    if (boundary.nearLeft || boundary.nearRight) {
      console.log(`[InfiniteScroll] Near boundary detected - Left: ${boundary.nearLeft}, Right: ${boundary.nearRight}`);
    }

    // Debounce rapid scroll events
    if (this.scrollTimeout) {
      clearTimeout(this.scrollTimeout);
    }

    this.scrollTimeout = setTimeout(() => {
      this.handleScrollBoundary(boundary);
    }, this.config.scrollDebounceMs);
  }

  private scrollTimeout: NodeJS.Timeout | null = null;

  /**
   * Detect if we're near scroll boundaries
   */
  private detectScrollBoundary(logicalRange: LogicalRange): ScrollBoundary {
    const totalBars = this.state.allOhlcData.length;
    const leftDistance = logicalRange.from;
    const rightDistance = totalBars - logicalRange.to;

    return {
      nearLeft: leftDistance < this.config.loadTriggerDistance,
      nearRight: rightDistance < this.config.loadTriggerDistance,
      leftDistance,
      rightDistance,
      currentRange: logicalRange,
    };
  }

  /**
   * Handle scroll boundary triggers
   */
  private async handleScrollBoundary(boundary: ScrollBoundary): Promise<void> {
    const promises: Promise<void>[] = [];

    // Load historical data (left side)
    if (boundary.nearLeft && this.config.bidirectionalLoading) {
      if (!this.isLoading(LoadDirection.HISTORICAL)) {
        console.log(`🔄 Infinite scroll: Loading historical data (left distance: ${boundary.leftDistance})`);
        promises.push(this.loadHistoricalData());
      }
    }

    // Load recent data (right side)
    if (boundary.nearRight && this.config.bidirectionalLoading) {
      if (!this.isLoading(LoadDirection.RECENT)) {
        console.log(`🔄 Infinite scroll: Loading recent data (right distance: ${boundary.rightDistance})`);
        promises.push(this.loadRecentData());
      }
    }

    // Execute loads in parallel
    if (promises.length > 0) {
      await Promise.allSettled(promises);
    }
  }

  /**
   * Load historical data (older data, left side of chart)
   */
  private async loadHistoricalData(): Promise<void> {
    const direction = LoadDirection.HISTORICAL;
    
    if (this.state.allOhlcData.length === 0) return;

    const oldestTime = this.getTimeAsSeconds(this.state.allOhlcData[0].time);
    const loadRequest = this.createLoadRequest(direction, oldestTime);

    try {
      await this.executeLoadRequest(loadRequest, direction);
    } catch (error) {
      this.handleLoadError(direction, error as Error);
    }
  }

  /**
   * Load recent data (newer data, right side of chart)
   */
  private async loadRecentData(): Promise<void> {
    const direction = LoadDirection.RECENT;
    
    if (this.state.allOhlcData.length === 0) return;

    const newestTime = this.getTimeAsSeconds(this.state.allOhlcData[this.state.allOhlcData.length - 1].time);
    const loadRequest = this.createLoadRequest(direction, newestTime);

    try {
      await this.executeLoadRequest(loadRequest, direction);
    } catch (error) {
      this.handleLoadError(direction, error as Error);
    }
  }

  /**
   * Create a load request
   */
  private createLoadRequest(direction: LoadDirection, timestamp: number): LoadRequest {
    return {
      id: `load_${++this.loadRequestCounter}_${Date.now()}`,
      direction,
      timestamp,
      limit: this.config.chunkSize,
      createdAt: Date.now(),
      timeoutMs: 30000, // 30 second timeout
    };
  }

  /**
   * Execute a load request
   */
  private async executeLoadRequest(request: LoadRequest, direction: LoadDirection): Promise<void> {
    this.startLoading(direction, request);

    try {
      let response: ChartResponse;

      // Use WebSocket if available, otherwise fall back to REST API
      if (this.socket && this.socket.connected) {
        response = await this.loadDataViaWebSocket(request, direction);
      } else {
        response = await this.loadDataViaRestAPI(request, direction);
      }

      if (response.ohlc.length === 0) {
        this.emitEvent('onStatusUpdated', `No more ${direction} data available`);
        this.finishLoading(direction);
        return;
      }

      const dataChunk = this.createDataChunk(response, direction, request.id);
      await this.processDataChunk(dataChunk);
      
      this.metrics.successfulLoads++;
      this.metrics.totalBarsLoaded += response.ohlc.length;
      
      this.emitEvent('onDataLoaded', dataChunk);
      this.finishLoading(direction);
      
    } catch (error) {
      this.metrics.failedLoads++;
      throw error;
    } finally {
      this.state.pendingRequests.delete(request.id);
      this.updateMetrics(request);
    }
  }

  /**
   * Load data via WebSocket
   */
  private async loadDataViaWebSocket(request: LoadRequest, direction: LoadDirection): Promise<ChartResponse> {
    if (!this.socket) {
      throw new Error('WebSocket not available');
    }

    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        this.socket?.off('chart_data', onChartData);
        this.socket?.off('error', onError);
        reject(new Error(`WebSocket request timeout for ${direction} data`));
      }, request.timeoutMs);

      const onChartData = (data: ChartDataResponse) => {
        clearTimeout(timeoutId);
        this.socket?.off('chart_data', onChartData);
        this.socket?.off('error', onError);

        if (data.error) {
          reject(new Error(data.error));
          return;
        }

        // Convert WebSocket response to ChartResponse format
        const chartResponse: ChartResponse = {
          ohlc: data.ohlc || [],
          volume: data.volume || [],
          instrument: data.instrument || this.state.instrument,
          timeframe: data.timeframe || this.state.timeframe,
          bars_returned: data.bars_returned || (data.ohlc?.length || 0),
          statistics: data.statistics ? {
            date_range: data.statistics.date_range || { start: '', end: '' },
            bars_count: data.statistics.bars_count || (data.ohlc?.length || 0)
          } : undefined,
          data_quality: data.data_quality ? {
            is_valid: data.data_quality.is_valid ?? true
          } : undefined
        };

        resolve(chartResponse);
      };

      const onError = (error: any) => {
        clearTimeout(timeoutId);
        this.socket?.off('chart_data', onChartData);
        this.socket?.off('error', onError);
        reject(new Error(error.message || 'WebSocket request failed'));
      };

      if (!this.socket) {
        reject(new Error('WebSocket connection not available'));
        return;
      }

      this.socket.once('chart_data', onChartData);
      this.socket.once('error', onError);

      // Create WebSocket request
      const wsRequest: ChartDataRequest = {
        instrument_id: this.state.instrument,
        timeframe: this.state.timeframe,
        limit: request.limit
      };

      // Add timestamp parameter for historical data
      if (direction === LoadDirection.HISTORICAL) {
        wsRequest.before_timestamp_seconds = request.timestamp;
      }

      this.socket.emit('request_chart_data', wsRequest);
    });
  }

  /**
   * Load data via REST API
   */
  private async loadDataViaRestAPI(request: LoadRequest, direction: LoadDirection): Promise<ChartResponse> {
    const beforeTimestamp = direction === LoadDirection.HISTORICAL ? request.timestamp : undefined;
    
    return await apiClient.getChartData(
      this.state.instrument,
      this.state.timeframe,
      {
        beforeTimestamp,
        limit: request.limit,
      }
    );
  }

  /**
   * Create data chunk from API response
   */
  private createDataChunk(response: ChartResponse, direction: LoadDirection, requestId: string): DataChunk {
    const range: DataRange = {
      startTime: this.getTimeAsSeconds(response.ohlc[0].time),
      endTime: this.getTimeAsSeconds(response.ohlc[response.ohlc.length - 1].time),
      barCount: response.ohlc.length,
      loading: false,
    };

    return {
      ohlc: response.ohlc,
      volume: response.volume,
      range,
      direction,
      requestId,
    };
  }

  /**
   * Process and merge data chunk
   */
  private async processDataChunk(chunk: DataChunk): Promise<void> {
    if (chunk.direction === LoadDirection.HISTORICAL) {
      // Prepend historical data
      this.state.allOhlcData = [...chunk.ohlc, ...this.state.allOhlcData];
      this.state.allVolumeData = [...chunk.volume, ...this.state.allVolumeData];
    } else {
      // Append recent data
      this.state.allOhlcData = [...this.state.allOhlcData, ...chunk.ohlc];
      this.state.allVolumeData = [...this.state.allVolumeData, ...chunk.volume];
    }

    // Add to loaded ranges
    this.state.loadedRanges.push(chunk.range);
    this.mergeOverlappingRanges();

    // Update memory stats and manage memory
    this.updateMemoryStats();
    
    if (this.config.enableMemoryManagement) {
      await this.manageMemory();
    }

    // Emit data loaded event to update the chart
    console.log(`📊 Infinite scroll: Data processed - ${chunk.direction} direction, ${chunk.ohlc.length} bars, total: ${this.state.allOhlcData.length}`);
    this.emitEvent('onDataLoaded', chunk);
  }

  /**
   * Manage memory by trimming old data if needed
   */
  private async manageMemory(): Promise<void> {
    if (this.state.allOhlcData.length <= this.config.maxBarsInMemory) {
      return;
    }

    const excessBars = this.state.allOhlcData.length - this.config.maxBarsInMemory;
    const barsToTrim = Math.max(excessBars, this.config.trimSize);

    // Determine which end to trim based on current logical range
    const shouldTrimLeft = this.shouldTrimFromLeft();
    
    if (shouldTrimLeft) {
      // Trim from beginning (oldest data)
      this.state.allOhlcData = this.state.allOhlcData.slice(barsToTrim);
      this.state.allVolumeData = this.state.allVolumeData.slice(barsToTrim);
      this.emitEvent('onMemoryTrimmed', barsToTrim, LoadDirection.HISTORICAL);
    } else {
      // Trim from end (newest data)
      this.state.allOhlcData = this.state.allOhlcData.slice(0, -barsToTrim);
      this.state.allVolumeData = this.state.allVolumeData.slice(0, -barsToTrim);
      this.emitEvent('onMemoryTrimmed', barsToTrim, LoadDirection.RECENT);
    }

    this.metrics.totalMemoryTrimmed += barsToTrim;
    this.updateMemoryStats();
    this.updateLoadedRanges();
  }

  /**
   * Determine if we should trim from the left side
   */
  private shouldTrimFromLeft(): boolean {
    if (!this.state.lastLogicalRange) return true;
    
    const totalBars = this.state.allOhlcData.length;
    const midPoint = totalBars / 2;
    const viewCenter = (this.state.lastLogicalRange.from + this.state.lastLogicalRange.to) / 2;
    
    // If viewing the right half, trim from left
    return viewCenter > midPoint;
  }

  /**
   * Update loaded ranges after memory trimming
   */
  private updateLoadedRanges(): void {
    if (this.state.allOhlcData.length === 0) {
      this.state.loadedRanges = [];
      return;
    }

    const startTime = this.getTimeAsSeconds(this.state.allOhlcData[0].time);
    const endTime = this.getTimeAsSeconds(this.state.allOhlcData[this.state.allOhlcData.length - 1].time);

    this.state.loadedRanges = [{
      startTime,
      endTime,
      barCount: this.state.allOhlcData.length,
      loading: false,
    }];
  }

  /**
   * Merge overlapping ranges
   */
  private mergeOverlappingRanges(): void {
    this.state.loadedRanges.sort((a, b) => a.startTime - b.startTime);
    
    const merged: DataRange[] = [];
    for (const range of this.state.loadedRanges) {
      const last = merged[merged.length - 1];
      
      if (last && range.startTime <= last.endTime) {
        // Merge overlapping ranges
        last.endTime = Math.max(last.endTime, range.endTime);
        last.barCount += range.barCount;
      } else {
        merged.push({ ...range });
      }
    }
    
    this.state.loadedRanges = merged;
  }

  /**
   * Loading state management
   */
  private startLoading(direction: LoadDirection, request: LoadRequest): void {
    const loadingState: LoadingState = {
      direction,
      isLoading: true,
      startTime: Date.now(),
    };

    this.state.loadingStates.set(direction, loadingState);
    this.state.pendingRequests.add(request.id);
    this.metrics.totalLoadRequests++;

    this.emitEvent('onLoadingStarted', direction, {
      startTime: request.timestamp,
      endTime: request.timestamp,
      barCount: 0,
      loading: true,
    });
  }

  private finishLoading(direction: LoadDirection): void {
    this.state.loadingStates.delete(direction);
    this.emitEvent('onLoadingFinished', direction);
  }

  private handleLoadError(direction: LoadDirection, error: Error): void {
    const loadingState = this.state.loadingStates.get(direction);
    if (loadingState) {
      loadingState.error = error.message;
    }

    this.emitEvent('onLoadingError', direction, error.message);
    this.finishLoading(direction);
  }

  private isLoading(direction: LoadDirection): boolean {
    return this.state.loadingStates.get(direction)?.isLoading ?? false;
  }

  /**
   * Utility methods
   */
  private getTimeAsSeconds(time: Time): number {
    if (typeof time === 'number') {
      // Validate timestamp is in reasonable range for Unix seconds
      if (time > 1e9 && time < 2e9) {
        return time; // Already in seconds
      }
      // Handle case where milliseconds were passed incorrectly
      if (time > 1e12 && time < 2e12) {
        console.warn(`Converting timestamp from milliseconds to seconds: ${time}`);
        return Math.floor(time / 1000);
      }
      throw new Error(`Invalid timestamp: ${time}. Expected Unix seconds between 1e9 and 2e9.`);
    }
    if (typeof time === 'string') {
      const timestamp = Math.floor(new Date(time).getTime() / 1000);
      if (isNaN(timestamp)) {
        throw new Error(`Invalid date string: ${time}`);
      }
      return timestamp;
    }
    return Math.floor(Date.now() / 1000);
  }

  private createEmptyMemoryStats(): MemoryStats {
    return {
      totalBars: 0,
      ohlcBars: 0,
      volumeBars: 0,
      memoryUsageEstimateMB: 0,
      oldestBarTime: 0,
      newestBarTime: 0,
      loadedRanges: [],
    };
  }

  private updateMemoryStats(): void {
    const ohlcBars = this.state.allOhlcData.length;
    const volumeBars = this.state.allVolumeData.length;
    
    // Rough estimate: each bar ~100 bytes (OHLC + metadata)
    const estimatedMB = (ohlcBars * 100 + volumeBars * 50) / (1024 * 1024);

    this.state.memoryStats = {
      totalBars: ohlcBars + volumeBars,
      ohlcBars,
      volumeBars,
      memoryUsageEstimateMB: estimatedMB,
      oldestBarTime: ohlcBars > 0 ? this.getTimeAsSeconds(this.state.allOhlcData[0].time) : 0,
      newestBarTime: ohlcBars > 0 ? this.getTimeAsSeconds(this.state.allOhlcData[ohlcBars - 1].time) : 0,
      loadedRanges: [...this.state.loadedRanges],
    };

    this.metrics.currentMemoryUsageMB = estimatedMB;
  }

  private updateMetrics(request: LoadRequest): void {
    const duration = Date.now() - request.createdAt;
    const totalRequests = this.metrics.totalLoadRequests;
    
    this.metrics.averageLoadTimeMs = 
      (this.metrics.averageLoadTimeMs * (totalRequests - 1) + duration) / totalRequests;
  }

  private emitEvent(eventName: keyof InfiniteScrollEvents, ...args: unknown[]): void {
    const handler = this.events[eventName] as Function;
    if (handler && typeof handler === 'function') {
      try {
        handler(...args);
      } catch (error) {
        console.error(`Error in infinite scroll event ${eventName}:`, error);
      }
    }
  }

  /**
   * Public getters
   */
  public getCurrentData() {
    return {
      ohlc: [...this.state.allOhlcData],
      volume: [...this.state.allVolumeData],
    };
  }

  public getState(): Readonly<DataManagerState> {
    return { ...this.state };
  }

  public getMetrics(): Readonly<InfiniteScrollMetrics> {
    return { ...this.metrics };
  }

  public getMemoryStats(): Readonly<MemoryStats> {
    return { ...this.state.memoryStats };
  }

  public isInitialized(): boolean {
    return this.state.initialized;
  }

  /**
   * Configuration updates
   */
  public updateConfig(config: Partial<InfiniteScrollConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * Cleanup
   */
  public destroy(): void {
    if (this.scrollTimeout) {
      clearTimeout(this.scrollTimeout);
    }
    
    this.state.pendingRequests.clear();
    this.state.loadingStates.clear();
    this.state.allOhlcData = [];
    this.state.allVolumeData = [];
    this.state.loadedRanges = [];
    this.state.initialized = false;
  }
}