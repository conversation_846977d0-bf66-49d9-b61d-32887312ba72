
import {
  CustomChartOptions,
  VolumeChartOptions,
  CustomCandlestickSeriesOptions,
  CustomVolumeSeriesOptions,
  OHLCData,
  VolumeData,
  ChartResponse,
  Timeframe,
  ChartElements,
  TimeRangeChangeHandler,
  TimeRange,
} from '../types';
import {
  InfiniteScrollConfig,
  DEFAULT_INFINITE_SCROLL_CONFIG,
  LoadDirection,
  DataChunk,
  InfiniteScrollEvents,
  DataRange,
  SlidingWindowConfig,
} from '../types/infinite-scroll';
import {
  Range,
  RangeState,
  RangeCalculationResult,
  RangeSwitchOptions,
  RangeEvents,
  DEFAULT_RANGE,
} from '../types/range';
import { EnhancedInfiniteScrollManager } from './enhanced-infinite-scroll-manager';
import { RangeManager, defaultRangeManager } from './range-manager';
import { NautilusDatafeed } from './nautilus-datafeed';

/**
 * Memory status interface
 */
interface MemoryStatus {
  dataPoints: number;
  memoryUtilization: number;
  isNearLimit: boolean;
}

/**
 * WebSocket interface for type-safe socket operations
 * Compatible with Socket.IO client interface used by Flask-SocketIO
 */
interface WebSocketInterface {
  emit(event: string, data?: any, callback?: Function): void;
  on(event: string, callback: (data?: any) => void): void;
  once(event: string, callback: (data?: any) => void): void;
  off(event: string, callback?: Function): void;
  connected: boolean;
  id?: string;
  // Socket.IO client specific methods
  connect?(): void;
  disconnect?(): void;
}

/**
 * Chart memory monitor for tracking memory usage and data limits
 */
class ChartMemoryMonitor {
  private currentDataPoints: number = 0;
  private memoryUtilization: number = 0;
  private maxDataPoints: number = 50000; // TradingView recommended limit

  updateFromDataChunk(chunk: DataChunk, currentData: { ohlc: OHLCData[]; volume: VolumeData[] }): void {
    // Add defensive checks
    this.currentDataPoints = currentData?.ohlc?.length || 0;
    this.memoryUtilization = this.maxDataPoints > 0 ? this.currentDataPoints / this.maxDataPoints : 0;
    
    // Optional: Use chunk data for more sophisticated memory calculations
    if (chunk && chunk.ohlc) {
      // Additional memory calculations based on chunk size could be added here
    }
  }

  updateFromResponse(response: ChartResponse): void {
    if (response.memory_status) {
      this.memoryUtilization = response.memory_status.memory_utilization || 0;
    }
    this.currentDataPoints = response.data_points || response.ohlc?.length || 0;
  }

  getStatus(): MemoryStatus {
    return {
      dataPoints: this.currentDataPoints,
      memoryUtilization: this.memoryUtilization,
      isNearLimit: this.memoryUtilization > 0.8
    };
  }

  setMaxDataPoints(maxPoints: number): void {
    this.maxDataPoints = maxPoints;
  }
}

/**
 * Enhanced chart manager with infinite scrolling capabilities
 */
export class EnhancedChartManager {
  private chart: IChartApi | null = null;
  private volumeChart: IChartApi | null = null;
  private candlestickSeries: ISeriesApi<'Candlestick'> | null = null;
  private volumeSeries: ISeriesApi<'Histogram'> | null = null;
  private dataManager: EnhancedInfiniteScrollManager | null = null;
  private datafeed: NautilusDatafeed | null = null;
  
  private isUpdatingSync = false;
  private isUpdatingData = false;
  private currentLogicalRange: LogicalRange | null = null;
  
  // Range management
  private rangeManager: RangeManager;
  private rangeState: RangeState;
  private rangeEventCallbacks: Map<keyof RangeEvents, Function[]> = new Map();
  
  // UI elements for status and loading
  private elements: ChartElements;
  private loadingIndicators: Map<LoadDirection, HTMLElement> = new Map();
  
  // Memory monitoring
  private memoryMonitor: ChartMemoryMonitor;

  constructor(
    private chartContainer: HTMLElement,
    private volumeContainer: HTMLElement,
    private instrumentId: string,
    private timeframe: Timeframe,
    elements: ChartElements,
    private infiniteScrollConfig: Partial<InfiniteScrollConfig> = {},
    rangeManager?: RangeManager,
    private socket?: WebSocketInterface | any
  ) {
    this.elements = elements;
    this.rangeManager = rangeManager || defaultRangeManager;
    
    // Validate socket interface compatibility with Flask-SocketIO client
    if (this.socket) {
      if (typeof this.socket.emit !== 'function' || 
          typeof this.socket.on !== 'function') {
        console.warn('Invalid socket provided - missing required methods, WebSocket features will be disabled');
        this.socket = undefined;
      } else if (typeof this.socket.connected !== 'boolean') {
        console.warn('Socket missing connection status, some features may not work correctly');
      }
    }
    
    // Initialize range state
    this.rangeState = {
      currentRange: DEFAULT_RANGE,
      isLoading: false,
    };
    
    // Initialize memory monitor
    this.memoryMonitor = new ChartMemoryMonitor();
    
    this.setupLoadingIndicators();
    this.setupRangeEventHandlers();
  }

  /**
   * Initialize charts and infinite scrolling
   */
  async initialize(initialData: ChartResponse): Promise<void> {
    try {
      // Create charts and series
      this.createCharts();
      this.createSeries();
      this.setupSynchronization();
      this.setupResize();

      // Initialize Nautilus datafeed with TradingView pattern
      this.datafeed = new NautilusDatafeed(this.instrumentId, this.timeframe, this.socket);
      await this.datafeed.initialize(initialData);

      // DISABLED: Initialize data manager - conflicts with NautilusDatafeed timestamp jumping
      // await this.initializeDataManager(initialData);

      // Set initial data using TradingView setData() pattern
      this.setInitialDataWithTradingViewPattern(initialData);

      // Setup infinite scrolling with datafeed pattern
      this.setupInfiniteScrollingWithDatafeed();

      // Setup alternative trigger mechanisms
      this.setupAlternativeTriggers();

      // Auto-focus chart for immediate keyboard navigation
      setTimeout(() => {
        this.chartContainer.focus();
        console.log('[InfiniteScroll] Chart container auto-focused for keyboard navigation');
      }, 500);

      this.showStatus('Chart initialized with TradingView-pattern infinite scrolling');
    } catch (error) {
      this.showError(`Failed to initialize chart: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * Create the main charts
   */
  private createCharts(): void {
    const chartOptions = this.getChartOptions();
    const volumeChartOptions = this.getVolumeChartOptions();

    this.chart = createChart(this.chartContainer, chartOptions);
    this.volumeChart = createChart(this.volumeContainer, volumeChartOptions);
  }

  /**
   * Create candlestick and volume series
   */
  private createSeries(): void {
    if (!this.chart || !this.volumeChart) {
      throw new Error('Charts must be created before series');
    }

    const candlestickOptions = this.getCandlestickOptions();
    const volumeOptions = this.getVolumeOptions();

    this.candlestickSeries = this.chart.addSeries(CandlestickSeries, candlestickOptions);
    this.volumeSeries = this.volumeChart.addSeries(HistogramSeries, volumeOptions);
  }

  /**
   * Initialize the infinite scroll data manager
   */
  private async initializeDataManager(initialData: ChartResponse): Promise<void> {
    const events: InfiniteScrollEvents = {
      onDataLoaded: this.handleDataLoaded.bind(this),
      onLoadingStarted: this.handleLoadingStarted.bind(this),
      onLoadingFinished: this.handleLoadingFinished.bind(this),
      onLoadingError: this.handleLoadingError.bind(this),
      onMemoryTrimmed: this.handleMemoryTrimmed.bind(this),
      onStatusUpdated: this.handleStatusUpdated.bind(this),
    };

    this.dataManager = new EnhancedInfiniteScrollManager(
      this.instrumentId,
      this.timeframe,
      this.infiniteScrollConfig,
      events,
      this.socket // Pass the socket for WebSocket data loading
    );

    await this.dataManager.initialize(initialData);
  }

  /**
   * Set initial data to charts
   */
  private setInitialData(data: ChartResponse): void {
    if (!this.candlestickSeries || !this.volumeSeries) {
      throw new Error('Series must be created before setting data');
    }

    // Validate and set OHLC data
    const validOhlcData = this.validateOhlcData(data.ohlc);
    if (validOhlcData.length === 0) {
      throw new Error('No valid OHLC data points found');
    }

    this.candlestickSeries.setData(validOhlcData);

    // Set volume data with colors
    if (data.volume && Array.isArray(data.volume) && data.volume.length > 0) {
      const validVolumeData = this.validateVolumeData(data.volume);
      if (validVolumeData.length > 0) {
        const volumeDataWithColors = this.addVolumeColors(validVolumeData, validOhlcData);
        this.volumeSeries.setData(volumeDataWithColors);
      }
    }

    // Update chart info
    this.updateChartInfo(data);

    // Fit content after data is set
    setTimeout(() => {
      if (this.chart && this.volumeChart) {
        this.chart.timeScale().fitContent();
        this.volumeChart.timeScale().fitContent();
      }
    }, 100);
  }

  /**
   * Setup infinite scrolling event handlers
   */
  private setupInfiniteScrolling(): void {
    if (!this.chart || !this.dataManager) return;

    // Subscribe to logical range changes for infinite scrolling
    this.chart.timeScale().subscribeVisibleLogicalRangeChange((range: LogicalRange | null) => {
      if (this.isUpdatingData || this.isUpdatingSync || !range) return;

      this.currentLogicalRange = range;
      this.dataManager!.checkAndLoadData(range);
    });

    // Setup keyboard navigation for infinite scroll
    this.setupKeyboardNavigation();
  }

  /**
   * Setup keyboard navigation for chart and infinite scroll
   */
  private setupKeyboardNavigation(): void {
    if (!this.chart || !this.chartContainer) return;

    // Make chart container focusable for keyboard interaction
    this.chartContainer.tabIndex = 0;
    this.chartContainer.style.outline = 'none'; // Remove focus outline for better UX

    // Add keyboard event listener
    this.chartContainer.addEventListener('keydown', (event: KeyboardEvent) => {
      // Only handle navigation keys
      const navigationKeys = ['ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown', 'Home', 'End'];
      if (!navigationKeys.includes(event.key)) return;

      // Prevent default browser behavior for navigation keys
      event.preventDefault();

      try {
        switch (event.key) {
          case 'ArrowLeft':
            this.shiftChart(-10); // Move left by 10 bars
            break;
          case 'ArrowRight':
            this.shiftChart(10); // Move right by 10 bars
            break;
          case 'ArrowUp':
            this.scaleChart(1 / 8, true); // Zoom in
            break;
          case 'ArrowDown':
            this.scaleChart(1 / 8, false); // Zoom out
            break;
          case 'Home':
            this.navigateToStart();
            break;
          case 'End':
            this.navigateToEnd();
            break;
        }
      } catch (error) {
        console.warn('Keyboard navigation error:', error);
      }
    });

    // Focus chart on click for keyboard accessibility
    this.chartContainer.addEventListener('click', () => {
      this.chartContainer.focus();
    });

    console.log('[InfiniteScroll] Keyboard navigation enabled - Arrow keys, Home/End supported');
  }

  /**
   * Shift chart horizontally (left/right navigation)
   */
  private shiftChart(diff: number): void {
    if (!this.chart) return;

    const currentRange = this.chart.timeScale().getVisibleLogicalRange();
    if (!currentRange) return;

    const rangeSize = currentRange.to - currentRange.from;
    const newRange: LogicalRange = {
      from: (currentRange.from + diff) as any,
      to: (currentRange.to + diff) as any,
    };

    // Ensure we don't go beyond reasonable bounds
    const totalBars = this.dataManager?.getCurrentData().ohlc.length || 0;
    if (totalBars > 0) {
      // Clamp the range to reasonable bounds
      if (newRange.from < -totalBars * 0.1) {
        newRange.from = (-totalBars * 0.1) as any;
        newRange.to = (newRange.from + rangeSize) as any;
      }
      if (newRange.to > totalBars * 1.1) {
        newRange.to = (totalBars * 1.1) as any;
        newRange.from = (newRange.to - rangeSize) as any;
      }
    }

    this.chart.timeScale().setVisibleLogicalRange(newRange);
    
    console.log(`[InfiniteScroll] Chart shifted by ${diff} bars (logical range: ${currentRange.from.toFixed(1)}-${currentRange.to.toFixed(1)} → ${newRange.from.toFixed(1)}-${newRange.to.toFixed(1)})`);
    
    // Manually trigger infinite scroll check since setVisibleLogicalRange may not trigger the event
    // due to isUpdatingSync flags blocking the event handler
    setTimeout(() => {
      if (this.dataManager) {
        this.dataManager.checkAndLoadData(newRange);
      }
    }, 50);
  }

  /**
   * Scale chart (zoom in/out)
   */
  private scaleChart(pct: number, zoomIn: boolean): void {
    if (!this.chart) return;

    const currentRange = this.chart.timeScale().getVisibleLogicalRange();
    if (!currentRange) return;

    const bars = currentRange.to - currentRange.from;
    const direction = zoomIn ? -1 : 1;
    const newRangeBars = bars * pct * direction + bars;
    
    // Ensure minimum zoom level
    if (newRangeBars < 5) return;

    this.chart.timeScale().setVisibleLogicalRange({
      to: currentRange.to,
      from: currentRange.to - newRangeBars,
    });

    console.log(`[InfiniteScroll] Chart ${zoomIn ? 'zoomed in' : 'zoomed out'} (bars: ${bars.toFixed(1)} → ${newRangeBars.toFixed(1)})`);
  }

  /**
   * Navigate to start of data
   */
  private navigateToStart(): void {
    if (!this.chart || !this.dataManager) return;

    const currentData = this.dataManager.getCurrentData();
    if (currentData.ohlc.length === 0) return;

    // Get current range size to maintain zoom level
    const currentRange = this.chart.timeScale().getVisibleLogicalRange();
    const rangeSize = currentRange ? (currentRange.to - currentRange.from) : 50;

    // Navigate to start of data (this should trigger historical data loading)
    const newRange: LogicalRange = {
      from: -5 as any, // Slightly before start to trigger infinite scroll
      to: (-5 + rangeSize) as any,
    };

    this.chart.timeScale().setVisibleLogicalRange(newRange);

    console.log(`[InfiniteScroll] Navigated to start of data (range: ${newRange.from.toFixed(1)}-${newRange.to.toFixed(1)})`);
    
    // Manually trigger infinite scroll check using datafeed
    setTimeout(async () => {
      if (this.datafeed) {
        try {
          const completeDataset = await this.datafeed.getBars(500);
          this.updateChartWithCompleteDataset(completeDataset);
        } catch (error) {
          console.error('Manual trigger error:', error);
        }
      }
    }, 50);
  }

  /**
   * Navigate to end of data (most recent)
   */
  private navigateToEnd(): void {
    if (!this.chart || !this.dataManager) return;

    const currentData = this.dataManager.getCurrentData();
    if (currentData.ohlc.length === 0) return;

    // Get current range size to maintain zoom level
    const currentRange = this.chart.timeScale().getVisibleLogicalRange();
    const rangeSize = currentRange ? (currentRange.to - currentRange.from) : 50;

    // Navigate to end of data (this should trigger recent data loading)
    const totalBars = currentData.ohlc.length;
    const newRange: LogicalRange = {
      from: (totalBars - rangeSize + 5) as any, // Slightly past end to trigger infinite scroll
      to: (totalBars + 5) as any,
    };

    this.chart.timeScale().setVisibleLogicalRange(newRange);

    console.log(`[InfiniteScroll] Navigated to end of data (range: ${newRange.from.toFixed(1)}-${newRange.to.toFixed(1)}, total bars: ${totalBars})`);
    
    // Manually trigger infinite scroll check using datafeed
    setTimeout(async () => {
      if (this.datafeed) {
        try {
          const completeDataset = await this.datafeed.getBars(500);
          this.updateChartWithCompleteDataset(completeDataset);
        } catch (error) {
          console.error('Manual trigger error:', error);
        }
      }
    }, 50);
  }

  /**
   * Setup chart synchronization
   */
  private setupSynchronization(): void {
    if (!this.chart || !this.volumeChart) return;

    // Price chart drives volume chart (time range sync)
    this.chart.timeScale().subscribeVisibleTimeRangeChange((timeRange: { from: Time; to: Time } | null) => {
      if (this.isUpdatingSync || this.isUpdatingData) return;
      this.syncTimeRange(timeRange);
    });

    // Logical range sync
    this.chart.timeScale().subscribeVisibleLogicalRangeChange((range: LogicalRange | null) => {
      if (this.isUpdatingSync || this.isUpdatingData) return;
      this.syncLogicalRange(range);
    });
  }

  /**
   * Sync time range between charts
   */
  private syncTimeRange(timeRange: { from: Time; to: Time } | null): void {
    try {
      if (timeRange &&
          timeRange.from !== null && timeRange.from !== undefined &&
          timeRange.to !== null && timeRange.to !== undefined &&
          this.volumeChart && this.volumeChart.timeScale) {
        this.isUpdatingSync = true;
        this.volumeChart.timeScale().setVisibleRange(timeRange);
        setTimeout(() => { this.isUpdatingSync = false; }, 10);
      }
    } catch (error: unknown) {
      this.isUpdatingSync = false;
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (errorMessage !== "Value is null") {
        console.log('Chart sync warning (time range):', errorMessage);
      }
    }
  }

  /**
   * Sync logical range between charts
   */
  private syncLogicalRange(range: LogicalRange | null): void {
    try {
      if (range &&
          range.from !== null && range.from !== undefined &&
          range.to !== null && range.to !== undefined &&
          this.volumeChart && this.volumeChart.timeScale) {
        this.isUpdatingSync = true;
        this.volumeChart.timeScale().setVisibleLogicalRange(range);
        setTimeout(() => { this.isUpdatingSync = false; }, 10);
      }
    } catch (error: unknown) {
      this.isUpdatingSync = false;
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (errorMessage !== "Value is null") {
        console.log('Chart sync warning (logical range):', errorMessage);
      }
    }
  }

  /**
   * Handle new data loaded by infinite scroll
   */
  private async handleDataLoaded(chunk: DataChunk): Promise<void> {
    if (!this.candlestickSeries || !this.volumeSeries || !this.dataManager) return;

    try {
      this.isUpdatingData = true;

      // Get current data from data manager
      const currentData = this.dataManager.getCurrentData();
      
      console.log(`🎯 Chart update: Received ${chunk.direction} data, updating chart with ${currentData.ohlc.length} total bars`);
      
      // Update memory monitor with new data
      this.memoryMonitor.updateFromDataChunk(chunk, currentData);
      
      // Store current logical range to restore it
      const currentRange = this.currentLogicalRange;

      // Update series with new data
      this.candlestickSeries.setData(currentData.ohlc);
      
      if (currentData.volume.length > 0) {
        const volumeWithColors = this.addVolumeColors(currentData.volume, currentData.ohlc);
        this.volumeSeries.setData(volumeWithColors);
      }

      // Restore logical range if we were loading historical data
      // This prevents the chart from jumping to the beginning
      if (chunk.direction === LoadDirection.HISTORICAL && currentRange) {
        setTimeout(() => {
          if (this.chart && this.volumeChart) {
            const newDataCount = chunk.ohlc.length;
            const adjustedRange: LogicalRange = {
              from: (currentRange.from + newDataCount) as any,
              to: (currentRange.to + newDataCount) as any,
            };
            
            this.chart.timeScale().setVisibleLogicalRange(adjustedRange);
            this.volumeChart.timeScale().setVisibleLogicalRange(adjustedRange);
          }
        }, 50);
      }

      // Handle data trimming notifications if memory coordinator is active
      const memoryStatus = this.memoryMonitor.getStatus();
      if (memoryStatus.isNearLimit) {
        this.handleDataTrimming(currentData.ohlc.length, chunk.ohlc.length);
      }

      this.showStatus(`Loaded ${chunk.ohlc.length} ${chunk.direction} bars`);
      
    } catch (error) {
      console.error('Error updating chart with new data:', error);
      this.showError(`Failed to update chart: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      this.isUpdatingData = false;
    }
  }

  /**
   * Handle loading started
   */
  private handleLoadingStarted(direction: LoadDirection, range: DataRange): void {
    this.showLoadingIndicator(direction, true);
    this.showStatus(`Loading ${direction} data...`);
  }

  /**
   * Handle loading finished
   */
  private handleLoadingFinished(direction: LoadDirection): void {
    this.showLoadingIndicator(direction, false);
  }

  /**
   * Handle loading error
   */
  private handleLoadingError(direction: LoadDirection, error: string): void {
    this.showLoadingIndicator(direction, false);
    this.showError(`Failed to load ${direction} data: ${error}`);
  }

  /**
   * Handle memory trimmed
   */
  private handleMemoryTrimmed(trimmedBars: number, direction: LoadDirection): void {
    this.showStatus(`Memory optimized: trimmed ${trimmedBars} bars from ${direction} data`);
    
    // Update chart with trimmed data
    if (this.candlestickSeries && this.volumeSeries && this.dataManager) {
      try {
        this.isUpdatingData = true;
        
        const currentData = this.dataManager.getCurrentData();
        this.candlestickSeries.setData(currentData.ohlc);
        
        if (currentData.volume.length > 0) {
          const volumeWithColors = this.addVolumeColors(currentData.volume, currentData.ohlc);
          this.volumeSeries.setData(volumeWithColors);
        }
        
        this.isUpdatingData = false;
      } catch (error) {
        this.isUpdatingData = false;
        console.error('Error updating chart after memory trim:', error);
      }
    }
  }

  /**
   * Handle status updates
   */
  private handleStatusUpdated(status: string): void {
    this.showStatus(status);
  }

  /**
   * Setup loading indicators
   */
  private setupLoadingIndicators(): void {
    // Create loading indicators if they don't exist
    if (!this.elements.infiniteScrollLoadingEl) {
      const loadingEl = document.createElement('div');
      loadingEl.id = 'infinite-scroll-loading';
      loadingEl.className = 'infinite-scroll-loading';
      loadingEl.innerHTML = `
        <div class="loading-historical" style="display: none;">
          <span class="spinner"></span> Loading historical data...
        </div>
        <div class="loading-recent" style="display: none;">
          <span class="spinner"></span> Loading recent data...
        </div>
      `;
      loadingEl.style.cssText = `
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 1000;
        background: rgba(0,0,0,0.8);
        color: white;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 12px;
        pointer-events: none;
      `;
      
      this.chartContainer.style.position = 'relative';
      this.chartContainer.appendChild(loadingEl);
      this.elements.infiniteScrollLoadingEl = loadingEl;
    }

    // Store references to individual loading indicators
    const historicalEl = this.elements.infiniteScrollLoadingEl.querySelector('.loading-historical') as HTMLElement;
    const recentEl = this.elements.infiniteScrollLoadingEl.querySelector('.loading-recent') as HTMLElement;
    
    if (historicalEl) this.loadingIndicators.set(LoadDirection.HISTORICAL, historicalEl);
    if (recentEl) this.loadingIndicators.set(LoadDirection.RECENT, recentEl);
  }

  /**
   * Show/hide loading indicator
   */
  private showLoadingIndicator(direction: LoadDirection, show: boolean): void {
    const indicator = this.loadingIndicators.get(direction);
    if (indicator) {
      indicator.style.display = show ? 'block' : 'none';
    }

    // Show/hide the container
    if (this.elements.infiniteScrollLoadingEl) {
      const anyVisible = Array.from(this.loadingIndicators.values()).some(el => el.style.display === 'block');
      this.elements.infiniteScrollLoadingEl.style.display = anyVisible ? 'block' : 'none';
    }
  }

  /**
   * Data validation methods
   */
  private validateOhlcData(data: OHLCData[]): OHLCData[] {
    if (!Array.isArray(data)) {
      console.warn('[EnhancedChartManager] OHLC data is not an array:', data);
      return [];
    }

    return data.filter((item, index) => {
      // Check for null or undefined item
      if (!item || item === null || item === undefined) {
        console.warn(`[EnhancedChartManager] Null/undefined OHLC item at index ${index}, removing`);
        return false;
      }

      // Check for valid time
      if (item.time === null || item.time === undefined) {
        console.warn(`[EnhancedChartManager] Invalid time at index ${index}:`, item.time);
        return false;
      }

      // Check for valid OHLC values (must be numbers and not null)
      const { open, high, low, close } = item;
      if (
        open === null || open === undefined || typeof open !== 'number' || isNaN(open) ||
        high === null || high === undefined || typeof high !== 'number' || isNaN(high) ||
        low === null || low === undefined || typeof low !== 'number' || isNaN(low) ||
        close === null || close === undefined || typeof close !== 'number' || isNaN(close)
      ) {
        console.warn(`[EnhancedChartManager] Invalid OHLC values at index ${index}:`, { open, high, low, close });
        return false;
      }

      // Check for logical price relationships
      if (high < low || high < open || high < close || low > open || low > close) {
        console.warn(`[EnhancedChartManager] Illogical OHLC relationships at index ${index}:`, { open, high, low, close });
        return false;
      }

      // Check for extremely large or small values that might indicate data corruption
      const values = [open, high, low, close];
      if (values.some(val => val <= 0 || val > 1e10 || val < 1e-6)) {
        console.warn(`[EnhancedChartManager] Extreme price values at index ${index}:`, { open, high, low, close });
        return false;
      }

      return true;
    });
  }

  private validateVolumeData(data: VolumeData[]): VolumeData[] {
    if (!Array.isArray(data)) {
      console.warn('[EnhancedChartManager] Volume data is not an array:', data);
      return [];
    }

    return data.filter((item, index) => {
      // Check for null or undefined volume
      if (!item || item === null || item === undefined) {
        console.warn(`[EnhancedChartManager] Null/undefined volume at index ${index}, removing`);
        return false;
      }

      // Check for valid time
      if (item.time === null || item.time === undefined) {
        console.warn(`[EnhancedChartManager] Invalid volume time at index ${index}:`, item.time);
        return false;
      }

      // Check for valid volume value
      if (item.value === null || item.value === undefined || typeof item.value !== 'number' || isNaN(item.value)) {
        console.warn(`[EnhancedChartManager] Invalid volume value at index ${index}:`, item.value);
        return false;
      }

      // Check for negative volume (not allowed)
      if (item.value < 0) {
        console.warn(`[EnhancedChartManager] Negative volume at index ${index}:`, item.value);
        return false;
      }

      // Check for extremely large volume that might indicate data corruption
      if (item.value > 1e12) {
        console.warn(`[EnhancedChartManager] Extreme volume value at index ${index}:`, item.value);
        return false;
      }

      return true;
    });
  }

  private addVolumeColors(volumeData: VolumeData[], ohlcData: OHLCData[]): VolumeData[] {
    return volumeData.map((item, index) => {
      const ohlcBar = ohlcData[index];
      if (!ohlcBar) return item;
      
      let barIsUp: boolean;
      
      if (ohlcBar.close > ohlcBar.open) {
        barIsUp = true;
      } else if (ohlcBar.close < ohlcBar.open) {
        barIsUp = false;
      } else {
        if (index > 0 && ohlcData[index - 1]) {
          barIsUp = ohlcBar.close >= ohlcData[index - 1].close;
        } else {
          barIsUp = true;
        }
      }
      
      return {
        ...item,
        color: barIsUp ? '#26a69a' : '#ef5350'
      };
    });
  }

  /**
   * Update chart information display
   */
  private updateChartInfo(data: ChartResponse): void {
    if (this.elements.barsCountEl) {
      this.elements.barsCountEl.textContent = data.bars_returned.toString();
    }

    if (this.elements.dateRangeEl && data.statistics) {
      this.elements.dateRangeEl.textContent = 
        `${data.statistics.date_range.start} to ${data.statistics.date_range.end}`;
    }

    if (this.elements.dataQualityEl && data.data_quality) {
      this.elements.dataQualityEl.textContent = 
        data.data_quality.is_valid ? 'Valid' : 'Issues detected';
      this.elements.dataQualityEl.className = 
        data.data_quality.is_valid ? 'text-success' : 'text-warning';
    }
  }

  /**
   * Status and error display
   */
  private showStatus(message: string): void {
    if (this.elements.infiniteScrollStatusEl) {
      this.elements.infiniteScrollStatusEl.textContent = message;
      this.elements.infiniteScrollStatusEl.className = 'alert alert-info';
      this.elements.infiniteScrollStatusEl.style.display = 'block';
      
      // Auto-hide after 3 seconds
      setTimeout(() => {
        if (this.elements.infiniteScrollStatusEl) {
          this.elements.infiniteScrollStatusEl.style.display = 'none';
        }
      }, 3000);
    }
    
    console.log(`[InfiniteScroll] ${message}`);
  }

  private showError(message: string): void {
    if (this.elements.errorEl) {
      this.elements.errorEl.textContent = message;
      this.elements.errorEl.style.display = 'block';
    }
    
    console.error(`[InfiniteScroll Error] ${message}`);
  }

  /**
   * Chart configuration methods
   */
  private getChartOptions(): CustomChartOptions {
    return {
      layout: {
        background: { type: ColorType.Solid, color: '#ffffff' },
        textColor: '#191919',
      },
      grid: {
        vertLines: { color: 'rgba(197, 203, 206, 0.5)', style: 0, visible: true },
        horzLines: { color: 'rgba(197, 203, 206, 0.5)', style: 0, visible: true }
      },
      timeScale: {
        timeVisible: true,
        secondsVisible: false,
        borderColor: 'rgba(197, 203, 206, 0.8)',
      },
      crosshair: {
        mode: CrosshairMode.Normal,
        vertLine: {
          color: '#758696',
          width: 1,
          style: LineStyle.Dashed,
        },
        horzLine: {
          color: '#758696',
          width: 1,
          style: LineStyle.Dashed,
        },
      },
      localization: {
        locale: 'en-US',
        dateFormat: 'dd/MM/yyyy',
      },
    };
  }

  private getVolumeChartOptions(): VolumeChartOptions {
    return {
      height: 150,
      layout: {
        background: { type: ColorType.Solid, color: '#ffffff' },
        textColor: '#191919',
      },
      grid: {
        vertLines: { color: 'rgba(197, 203, 206, 0.5)', style: 0, visible: true },
        horzLines: { color: 'rgba(197, 203, 206, 0.5)', style: 0, visible: true }
      },
      timeScale: {
        visible: false
      },
      rightPriceScale: {
        scaleMargins: {
          top: 0.1,
          bottom: 0.1,
        },
        autoScale: true,
        mode: 0,
        invertScale: false,
        alignLabels: true,
        entireTextOnly: false,
        visible: true,
        borderVisible: true,
        borderColor: '#cccccc',
        textColor: '#191919',
        minimumWidth: 0
      },
      localization: {
        locale: 'en-US',
        dateFormat: 'dd/MM/yyyy',
      },
    };
  }

  private getCandlestickOptions(): CustomCandlestickSeriesOptions {
    return {
      upColor: '#26a69a',
      downColor: '#ef5350',
      borderVisible: false,
      wickUpColor: '#26a69a',
      wickDownColor: '#ef5350',
      priceFormat: {
        type: 'price',
        precision: 2,
        minMove: 0.01,
      },
      lastValueVisible: true,
      priceLineVisible: true,
      priceLineWidth: 1,
      priceLineColor: '#5294ff',
      priceLineStyle: LineStyle.Dashed,
    };
  }

  private getVolumeOptions(): CustomVolumeSeriesOptions {
    return {
      color: '#26a69a',
      priceFormat: {
        type: 'volume',
        precision: 0,
      },
      priceScaleId: '',
    };
  }

  /**
   * Resize handler
   */
  private setupResize(): void {
    window.addEventListener('resize', () => {
      if (this.chart && this.volumeChart) {
        this.chart.applyOptions({
          width: this.chartContainer.clientWidth,
          height: this.chartContainer.clientHeight
        });

        this.volumeChart.applyOptions({
          width: this.volumeContainer.clientWidth,
          height: this.volumeContainer.clientHeight
        });
      }
    });
  }

  /**
   * Range Management Methods
   */
  
  /**
   * Setup range event handlers
   */
  private setupRangeEventHandlers(): void {
    // Initialize event callback maps
    this.rangeEventCallbacks.set('range:changed', []);
    this.rangeEventCallbacks.set('range:loading', []);
    this.rangeEventCallbacks.set('range:loaded', []);
    this.rangeEventCallbacks.set('range:error', []);
    this.rangeEventCallbacks.set('range:validated', []);
  }

  /**
   * Switch to a new range
   */
  public async setRange(range: Range, options: RangeSwitchOptions = {}): Promise<void> {
    if (this.rangeState.isLoading && !options.preserveData) {
      this.showError('Range change already in progress');
      return;
    }

    try {
      // Update loading state
      this.setRangeLoading(true);
      this.emitRangeEvent('range:loading', range);

      // Calculate range
      const calculation = this.rangeManager.calculateRange(range, this.timeframe);
      
      // Validate range
      const validation = this.rangeManager.validateRange(calculation);
      this.emitRangeEvent('range:validated', validation);
      
      if (!validation.isValid) {
        throw new Error(`Invalid range: ${validation.errors.join(', ')}`);
      }

      // Update range state
      this.rangeState.currentRange = range;
      this.rangeState.lastCalculation = calculation;
      this.rangeState.error = undefined;

      // Update URL if requested
      if (options.updateUrl !== false) {
        this.updateUrlWithRange(range);
      }

      // Update UI
      this.updateRangeUI(range, calculation);

      // Load data for new range (integrate with infinite scroll)
      if (!options.preserveData) {
        await this.loadRangeData(calculation, options);
      }

      // Emit success event
      this.emitRangeEvent('range:changed', range, calculation);
      this.emitRangeEvent('range:loaded', range, calculation);

      this.showStatus(`Range switched to ${range}`);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.rangeState.error = errorMessage;
      this.emitRangeEvent('range:error', range, errorMessage);
      this.showError(`Failed to switch range: ${errorMessage}`);
    } finally {
      this.setRangeLoading(false);
    }
  }

  /**
   * Set custom range with specific dates
   */
  public async setCustomRange(startDate: Date, endDate: Date, label?: string, options: RangeSwitchOptions = {}): Promise<void> {
    try {
      this.setRangeLoading(true);

      const customRange = {
        startDate,
        endDate,
        label: label || `${startDate.toLocaleDateString()} to ${endDate.toLocaleDateString()}`,
      };

      const calculation = this.rangeManager.calculateCustomRange(customRange, this.timeframe);
      const validation = this.rangeManager.validateRange(calculation);

      if (!validation.isValid) {
        throw new Error(`Invalid custom range: ${validation.errors.join(', ')}`);
      }

      // Update range state
      this.rangeState.currentRange = 'CUSTOM';
      this.rangeState.customRange = customRange;
      this.rangeState.lastCalculation = calculation;
      this.rangeState.error = undefined;

      // Update UI
      this.updateRangeUI('CUSTOM', calculation);

      // Load data for custom range
      if (!options.preserveData) {
        await this.loadRangeData(calculation, options);
      }

      this.emitRangeEvent('range:changed', 'CUSTOM', calculation);
      this.showStatus(`Custom range set: ${customRange.label}`);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.rangeState.error = errorMessage;
      this.emitRangeEvent('range:error', 'CUSTOM', errorMessage);
      this.showError(`Failed to set custom range: ${errorMessage}`);
    } finally {
      this.setRangeLoading(false);
    }
  }

  /**
   * Load data for a specific range calculation
   */
  private async loadRangeData(calculation: RangeCalculationResult, options: RangeSwitchOptions): Promise<void> {
    if (!this.dataManager) {
      throw new Error('Data manager not initialized');
    }

    // Show loading indicator if requested
    if (options.showLoadingIndicator !== false) {
      this.showRangeLoadingIndicator(true);
    }

    try {
      // For now, we'll integrate with the existing infinite scroll system
      // In a production system, you'd want to implement range-specific data loading
      
      // Calculate new data range based on range calculation
      const dataRange: DataRange = {
        startTime: Math.floor(calculation.startTimestamp / 1000),
        endTime: Math.floor(calculation.endTimestamp / 1000),
        barCount: calculation.estimatedBars,
        loading: false,
      };

      // Request data manager to load data for this range
      // This would require extending the infinite scroll data manager
      // to support range-based loading
      
      this.showStatus(`Loading ${calculation.estimatedBars} estimated bars for range ${calculation.range}`);

    } catch (error) {
      throw new Error(`Failed to load range data: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      if (options.showLoadingIndicator !== false) {
        this.showRangeLoadingIndicator(false);
      }
    }
  }

  /**
   * Update UI elements with range information
   */
  private updateRangeUI(range: Range, calculation: RangeCalculationResult): void {
    // Update range buttons
    this.updateRangeButtons(range);

    // Update chart info
    if (this.elements.currentRangeEl) {
      this.elements.currentRangeEl.textContent = range;
    }

    if (this.elements.estimatedBarsEl) {
      this.elements.estimatedBarsEl.textContent = calculation.estimatedBars.toString();
    }

    // Update date range display
    if (this.elements.dateRangeEl) {
      const startDate = new Date(calculation.startTimestamp).toLocaleDateString();
      const endDate = new Date(calculation.endTimestamp).toLocaleDateString();
      this.elements.dateRangeEl.textContent = `${startDate} to ${endDate}`;
    }
  }

  /**
   * Update range button states
   */
  private updateRangeButtons(activeRange: Range): void {
    const rangeButtons = document.querySelectorAll('.range-btn');
    rangeButtons.forEach(button => {
      const buttonRange = (button as HTMLElement).dataset.range as Range;
      if (buttonRange === activeRange) {
        button.classList.add('active');
      } else {
        button.classList.remove('active');
      }
    });
  }

  /**
   * Update URL with current range
   */
  private updateUrlWithRange(range: Range): void {
    const url = new URL(window.location.href);
    url.searchParams.set('range', range.toLowerCase());
    
    if (this.rangeState.customRange && range === 'CUSTOM') {
      url.searchParams.set('start', this.rangeState.customRange.startDate.toISOString().split('T')[0]);
      url.searchParams.set('end', this.rangeState.customRange.endDate.toISOString().split('T')[0]);
    } else {
      url.searchParams.delete('start');
      url.searchParams.delete('end');
    }
    
    window.history.replaceState({}, '', url.toString());
  }

  /**
   * Load range from URL parameters
   */
  public loadRangeFromUrl(): void {
    const url = new URL(window.location.href);
    const rangeParam = url.searchParams.get('range');
    
    if (rangeParam) {
      const range = rangeParam.toUpperCase() as Range;
      
      if (range === 'CUSTOM') {
        const startParam = url.searchParams.get('start');
        const endParam = url.searchParams.get('end');
        
        if (startParam && endParam) {
          const startDate = new Date(startParam);
          const endDate = new Date(endParam);
          this.setCustomRange(startDate, endDate, undefined, { updateUrl: false });
          return;
        }
      }
      
      // Validate range and set it
      if (['1D', '1W', '1M', '3M', '6M', '1Y', 'YTD', 'ALL'].includes(range)) {
        this.setRange(range, { updateUrl: false });
        return;
      }
    }
    
    // Default to 1D if no valid range in URL
    this.setRange(DEFAULT_RANGE, { updateUrl: false });
  }

  /**
   * Set range loading state
   */
  private setRangeLoading(loading: boolean): void {
    this.rangeState.isLoading = loading;
    this.showRangeLoadingIndicator(loading);
  }

  /**
   * Show/hide range loading indicator
   */
  private showRangeLoadingIndicator(show: boolean): void {
    const indicator = document.getElementById('range-loading');
    if (indicator) {
      indicator.style.display = show ? 'inline' : 'none';
    }

    // Disable range buttons during loading
    const rangeButtons = document.querySelectorAll('.range-btn');
    rangeButtons.forEach(button => {
      if (show) {
        button.classList.add('loading');
        (button as HTMLButtonElement).disabled = true;
      } else {
        button.classList.remove('loading');
        (button as HTMLButtonElement).disabled = false;
      }
    });
  }

  /**
   * Range event management
   */
  public onRangeEvent<K extends keyof RangeEvents>(event: K, callback: RangeEvents[K]): void {
    if (!this.rangeEventCallbacks.has(event)) {
      this.rangeEventCallbacks.set(event, []);
    }
    this.rangeEventCallbacks.get(event)!.push(callback);
  }

  public offRangeEvent<K extends keyof RangeEvents>(event: K, callback: RangeEvents[K]): void {
    const callbacks = this.rangeEventCallbacks.get(event);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  private emitRangeEvent<K extends keyof RangeEvents>(event: K, ...args: Parameters<RangeEvents[K]>): void {
    const callbacks = this.rangeEventCallbacks.get(event);
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          (callback as Function)(...args);
        } catch (error) {
          console.error(`Error in range event callback for ${event}:`, error);
        }
      });
    }
  }

  /**
   * Get current range state
   */
  public getRangeState(): RangeState {
    return { ...this.rangeState };
  }

  /**
   * Get range calculation result
   */
  public getRangeCalculation(): RangeCalculationResult | undefined {
    return this.rangeState.lastCalculation;
  }

  /**
   * Check if range requires real-time updates
   */
  public requiresRealTimeUpdates(): boolean {
    return this.rangeManager.requiresRealTimeUpdates(this.rangeState.currentRange);
  }

  /**
   * Get suggested preload ranges
   */
  public getSuggestedPreloadRanges(): Range[] {
    return this.rangeManager.getSuggestedPreloadRanges(this.rangeState.currentRange);
  }

  /**
   * Set initial data using TradingView setData() pattern
   */
  private setInitialDataWithTradingViewPattern(initialData: ChartResponse): void {
    if (!this.candlestickSeries || !this.volumeSeries || !this.datafeed) return;

    try {
      this.isUpdatingData = true;

      // Use setData() pattern - TradingView official approach
      this.candlestickSeries.setData(initialData.ohlc);
      
      if (initialData.volume.length > 0) {
        const volumeWithColors = this.addVolumeColors(initialData.volume, initialData.ohlc);
        this.volumeSeries.setData(volumeWithColors);
      }

      console.log(`[TradingView Pattern] Initial data set with ${initialData.ohlc.length} bars`);
      
    } catch (error) {
      console.error('Error setting initial data with TradingView pattern:', error);
      this.showError(`Failed to set initial data: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      this.isUpdatingData = false;
    }
  }

  /**
   * Setup infinite scrolling with datafeed pattern (TradingView official approach)
   */
  private setupInfiniteScrollingWithDatafeed(): void {
    if (!this.chart || !this.datafeed) return;

    // TradingView Official Pattern: subscribeVisibleLogicalRangeChange
    this.chart.timeScale().subscribeVisibleLogicalRangeChange(async (logicalRange) => {
      // Enhanced diagnostic logging to identify exact failure condition
      const triggerDistance = this.infiniteScrollConfig.loadTriggerDistance || 5;
      const shouldTrigger = logicalRange?.from !== undefined && logicalRange.from < triggerDistance;
      
      console.log(`[DEBUG] Logical range change:`, {
        range: logicalRange,
        from: logicalRange?.from,
        to: logicalRange?.to,
        triggerDistance: triggerDistance,
        shouldTrigger: shouldTrigger,
        isUpdating: this.isUpdatingData,
        datafeedExists: !!this.datafeed,
        datafeedType: this.datafeed ? this.datafeed.constructor.name : 'null'
      });
      
      // Check each condition individually with detailed logging
      if (!logicalRange) {
        console.log('[DEBUG] ❌ Skipping trigger - No logical range provided');
        return;
      }
      
      if (!this.datafeed) {
        console.log('[DEBUG] ❌ Skipping trigger - Datafeed not initialized');
        console.log('[DEBUG] 🔧 Attempting to reinitialize datafeed...');
        // Try to reinitialize datafeed if missing
        this.initializeDatafeed();
        return;
      }
      
      if (this.isUpdatingData) {
        console.log('[DEBUG] ⏳ Skipping trigger - Chart is currently updating data');
        return;
      }
      
      console.log('[DEBUG] ✅ All conditions met, checking trigger criteria...');

      try {
        // TradingView pattern: Check BOTH boundaries for bidirectional infinite scroll
        const triggerDistance = this.infiniteScrollConfig.loadTriggerDistance || 5;
        const currentDataCount = this.datafeed.getDatasetSize();
        
        // Check both left and right edges
        const isNearLeftEdge = logicalRange.from < triggerDistance;
        const isNearRightEdge = logicalRange.to > (currentDataCount - triggerDistance);
        
        console.log(`[DEBUG] Bidirectional trigger check:`, {
          fromPosition: logicalRange.from,
          toPosition: logicalRange.to,
          triggerDistance: triggerDistance,
          currentDataCount: currentDataCount,
          isNearLeftEdge: isNearLeftEdge,
          isNearRightEdge: isNearRightEdge,
          leftDistance: logicalRange.from,
          rightDistance: currentDataCount - logicalRange.to,
          chartPosition: logicalRange.from > currentDataCount * 0.8 ? 'RIGHT_EDGE (recent data)' : logicalRange.from < currentDataCount * 0.2 ? 'LEFT_EDGE (historical)' : 'MIDDLE'
        });
        
        if (isNearLeftEdge) {
          console.log(`🔄 [InfiniteScroll] LEFT TRIGGER ACTIVATED! Loading historical data (left distance: ${logicalRange.from})`);
          
          // Set updating flag to prevent concurrent requests
          this.isUpdatingData = true;
          
          try {
            const numberBarsToLoad = this.infiniteScrollConfig.chunkSize || 500;
            console.log(`[InfiniteScroll] Requesting ${numberBarsToLoad} historical bars from datafeed...`);
            
            // Check if we should use incremental updates (update() method) or full replacement (setData())
            const shouldUseIncrementalUpdates = this.datafeed.shouldUseIncrementalUpdates();
            
            if (shouldUseIncrementalUpdates) {
              // NEW: Use incremental update() method for memory efficiency
              console.log(`[InfiniteScroll] Using incremental update() pattern for historical data`);
              
              const incrementalResult = await this.datafeed.getNewBarsHistorical(numberBarsToLoad);
              
              if (incrementalResult.newBars.length > 0) {
                this.updateChartWithIncrementalData(
                  incrementalResult.newBars, 
                  incrementalResult.newVolume, 
                  'historical',
                  incrementalResult.trimmed
                );
                console.log(`✅ [InfiniteScroll] Successfully loaded ${incrementalResult.newBars.length} historical bars via update() method!`);
              } else {
                console.log(`[InfiniteScroll] No new historical bars available`);
              }
              
            } else {
              // Fallback: Use complete dataset replacement when memory pressure is high
              console.log(`[InfiniteScroll] Using setData() pattern due to memory pressure`);
              
              const completeDataset = await this.datafeed.getBarsHistorical(numberBarsToLoad);
              this.updateChartWithCompleteDataset(completeDataset);
              console.log(`✅ [InfiniteScroll] Successfully loaded historical data via setData() method!`);
            }
            
          } finally {
            this.isUpdatingData = false;
          }
          
        } else if (isNearRightEdge) {
          console.log(`🔄 [InfiniteScroll] RIGHT TRIGGER ACTIVATED! Loading recent data (right distance: ${currentDataCount - logicalRange.to})`);
          
          // Set updating flag to prevent concurrent requests
          this.isUpdatingData = true;
          
          try {
            const numberBarsToLoad = this.infiniteScrollConfig.chunkSize || 500;
            console.log(`[InfiniteScroll] Requesting ${numberBarsToLoad} recent bars from datafeed...`);
            
            // Check if we should use incremental updates (update() method) or full replacement (setData())
            const shouldUseIncrementalUpdates = this.datafeed.shouldUseIncrementalUpdates();
            
            if (shouldUseIncrementalUpdates) {
              // NEW: Use incremental update() method for memory efficiency
              console.log(`[InfiniteScroll] Using incremental update() pattern for recent data`);
              
              const incrementalResult = await this.datafeed.getNewBarsRecent(numberBarsToLoad);
              
              if (incrementalResult.newBars.length > 0) {
                this.updateChartWithIncrementalData(
                  incrementalResult.newBars, 
                  incrementalResult.newVolume, 
                  'recent',
                  incrementalResult.trimmed
                );
                console.log(`✅ [InfiniteScroll] Successfully loaded ${incrementalResult.newBars.length} recent bars via update() method!`);
              } else {
                console.log(`[InfiniteScroll] No new recent bars available`);
              }
              
            } else {
              // Fallback: Use complete dataset replacement when memory pressure is high
              console.log(`[InfiniteScroll] Using setData() pattern due to memory pressure`);
              
              const completeDataset = await this.datafeed.getBarsRecent(numberBarsToLoad);
              this.updateChartWithCompleteDataset(completeDataset);
              console.log(`✅ [InfiniteScroll] Successfully loaded recent data via setData() method!`);
            }
            
          } finally {
            this.isUpdatingData = false;
          }
          
        } else {
          console.log(`[DEBUG] ⏸️ No trigger needed - Left: ${logicalRange.from} >= ${triggerDistance}, Right: ${currentDataCount - logicalRange.to} >= ${triggerDistance}`);
          
          // Provide helpful guidance for users
          if (logicalRange.from > currentDataCount * 0.8) {
            console.log(`💡 [InfiniteScroll] TIP: You're at the RIGHT edge. Scroll further RIGHT to load newer data, or LEFT to see historical data.`);
          } else if (logicalRange.from < currentDataCount * 0.2) {
            console.log(`💡 [InfiniteScroll] TIP: You're at the LEFT edge. Scroll further LEFT to load historical data, or RIGHT to see recent data.`);
          }
        }
        
      } catch (error) {
        this.isUpdatingData = false; // Reset flag on error
        console.error('❌ Error in TradingView pattern infinite scroll:', error);
        this.showError(`Infinite scroll error: ${error instanceof Error ? error.message : String(error)}`);
      }
    });

    console.log('[TradingView Pattern] Infinite scrolling enabled with subscribeVisibleLogicalRangeChange');
  }

  /**
   * Initialize or reinitialize the datafeed
   */
  private initializeDatafeed(): void {
    if (!this.datafeed && this.socket) {
      console.log('[DEBUG] 🔧 Creating new NautilusDatafeed instance...');
      this.datafeed = new NautilusDatafeed(this.instrumentId, this.timeframe, this.socket);
      console.log('[DEBUG] ✅ Datafeed created but not initialized (needs initial data)');
    }
  }

  /**
   * Setup alternative trigger mechanisms for infinite scroll
   */
  private setupAlternativeTriggers(): void {
    if (!this.chart || !this.chartContainer) return;

    // Method 1: Mouse wheel at edges
    this.chartContainer.addEventListener('wheel', (event) => {
      const currentRange = this.chart?.timeScale().getVisibleLogicalRange();
      if (currentRange && currentRange.from < 10 && event.deltaX < 0) {
        this.triggerHistoricalLoad();
      }
    });

    // Method 2: Programmatic trigger for testing
    (window as any).triggerInfiniteScroll = () => {
      this.triggerHistoricalLoad();
    };

    console.log('[InfiniteScroll] Alternative trigger mechanisms enabled (mouse wheel, programmatic)');
  }

  /**
   * Trigger historical data load manually
   */
  private async triggerHistoricalLoad(): Promise<void> {
    if (!this.datafeed) return;
    
    console.log('🔄 Manual infinite scroll trigger activated');
    try {
      const completeDataset = await this.datafeed.getBars(500);
      this.updateChartWithCompleteDataset(completeDataset);
    } catch (error) {
      console.error('Manual trigger failed:', error);
    }
  }

  /**
   * Update chart with complete dataset (TradingView setData() pattern)
   * Used only when incremental updates are not suitable (e.g., high memory pressure)
   */
  private updateChartWithCompleteDataset(dataset: { ohlc: OHLCData[]; volume: VolumeData[] }): void {
    if (!this.candlestickSeries || !this.volumeSeries) return;

    try {
      this.isUpdatingData = true;

      // Store current logical range to restore it after data update
      const currentRange = this.chart?.timeScale().getVisibleLogicalRange();
      
      // TradingView Official Pattern: Use setData() for complete dataset replacement
      this.candlestickSeries.setData(dataset.ohlc);
      
      if (dataset.volume.length > 0) {
        const volumeWithColors = this.addVolumeColors(dataset.volume, dataset.ohlc);
        this.volumeSeries.setData(volumeWithColors);
      }

      // Restore logical range for smooth scrolling experience
      if (currentRange && this.chart) {
        setTimeout(() => {
          this.chart?.timeScale().setVisibleLogicalRange(currentRange);
        }, 50);
      }

      console.log(`📊 TradingView setData() Pattern: Chart updated with complete dataset (${dataset.ohlc.length} bars)`);
      
    } catch (error) {
      console.error('Error updating chart with complete dataset:', error);
      this.showError(`Chart update error: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      this.isUpdatingData = false;
    }
  }

  /**
   * Update chart with incremental data using update() method (TradingView recommended pattern)
   * This is the preferred method for memory-efficient updates
   */
  private updateChartWithIncrementalData(newBars: OHLCData[], newVolume: VolumeData[], direction: 'historical' | 'recent', trimmed?: number): void {
    if (!this.candlestickSeries || !this.volumeSeries) return;

    try {
      this.isUpdatingData = true;

      console.log(`🔄 [InfiniteScroll] Incremental update: Adding ${newBars.length} ${direction} bars using update() method`);

      // Store current logical range for smooth scrolling experience
      const currentRange = this.chart?.timeScale().getVisibleLogicalRange();
      let adjustedRange = currentRange;

      if (direction === 'recent') {
        // Recent data: TradingView update() method works perfectly for newer data
        // Add in chronological order (each bar must be newer than the last)
        for (let i = 0; i < newBars.length; i++) {
          try {
            this.candlestickSeries.update(newBars[i] as CandlestickData);
            
            if (newVolume[i]) {
              const volumeWithColor = this.addVolumeColor(newVolume[i], newBars[i]);
              this.volumeSeries.update(volumeWithColor as HistogramData);
            }
          } catch (updateError) {
            console.warn(`[InfiniteScroll] update() failed for bar ${i}, falling back to setData():`, updateError);
            // Fallback to setData() if update() fails
            this.fallbackToSetDataMethod(direction, trimmed);
            return;
          }
        }
        
        console.log(`✅ [InfiniteScroll] Recent data: ${newBars.length} bars added via update() method`);

      } else {
        // Historical data: TradingView update() method CANNOT add older data
        // Must use setData() for historical data as update() only supports newer timestamps
        console.log(`[InfiniteScroll] Historical data requires setData() method (update() only supports newer data)`);
        this.fallbackToSetDataMethod(direction, trimmed);
        return;
      }

      // Handle trimming notification
      if (trimmed && trimmed > 0) {
        this.showStatus(`Memory optimized: ${trimmed} bars trimmed during ${direction} data load`);
        console.log(`🔄 [InfiniteScroll] Memory management: ${trimmed} bars trimmed from opposite edge`);
      }

      // Restore logical range for smooth scrolling experience
      if (adjustedRange && this.chart) {
        setTimeout(() => {
          this.chart?.timeScale().setVisibleLogicalRange(adjustedRange!);
        }, 50);
      }

      console.log(`✅ [InfiniteScroll] Incremental update complete: ${newBars.length} ${direction} bars added via update() method`);
      
    } catch (error) {
      console.error(`Error updating chart with incremental ${direction} data:`, error);
      this.showError(`Incremental update error: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      this.isUpdatingData = false;
    }
  }

  /**
   * Add volume color for a single volume data point
   */
  private addVolumeColor(volume: VolumeData, ohlcBar: OHLCData): VolumeData {
    let barIsUp: boolean;
    
    if (ohlcBar.close > ohlcBar.open) {
      barIsUp = true;
    } else if (ohlcBar.close < ohlcBar.open) {
      barIsUp = false;
    } else {
      // For doji bars, assume up if we can't determine direction
      barIsUp = true;
    }
    
    return {
      ...volume,
      color: barIsUp ? '#26a69a' : '#ef5350'
    };
  }

  /**
   * Fallback to setData() method when update() method cannot be used
   * This handles historical data and error cases
   */
  private fallbackToSetDataMethod(direction: 'historical' | 'recent', trimmed?: number): void {
    if (!this.datafeed) return;

    try {
      console.log(`🔄 [InfiniteScroll] Using setData() fallback for ${direction} data`);
      
      // Get complete dataset from datafeed
      const completeDataset = this.datafeed.getCurrentData();
      this.updateChartWithCompleteDataset(completeDataset);
      
      // Handle trimming notification
      if (trimmed && trimmed > 0) {
        this.showStatus(`Memory optimized: ${trimmed} bars trimmed during ${direction} data load`);
        console.log(`🔄 [InfiniteScroll] Memory management: ${trimmed} bars trimmed from opposite edge`);
      }
      
      console.log(`✅ [InfiniteScroll] Successfully loaded ${direction} data via setData() fallback`);
      
    } catch (error) {
      console.error(`Error in setData() fallback for ${direction} data:`, error);
      this.showError(`Chart update error: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Public API methods
   */
  public updateSingleBar(ohlc: OHLCData, volume: VolumeData): void {
    if (!this.candlestickSeries || !this.volumeSeries || !this.datafeed) return;

    try {
      // NEW: Prefer update() method for single bar updates (TradingView recommended)
      console.log(`[TradingView Pattern] Single bar update via efficient update() method`);
      
      // Update datafeed data
      const completeDataset = this.datafeed.updateLastBar(ohlc, volume);
      
      // Use direct update() method for single bar - more efficient than setData()
      this.candlestickSeries.update(ohlc as CandlestickData);
      
      const volumeWithColor = this.addVolumeColor(volume, ohlc);
      this.volumeSeries.update(volumeWithColor as HistogramData);
      
      console.log(`[TradingView Pattern] Single bar updated efficiently via update() method`);
      
    } catch (error) {
      console.error('Error updating single bar with update() pattern:', error);
      
      // Fallback to setData() if update() fails for some reason
      try {
        const completeDataset = this.datafeed.updateLastBar(ohlc, volume);
        this.candlestickSeries.setData(completeDataset.ohlc);
        
        if (completeDataset.volume.length > 0) {
          const volumeWithColors = this.addVolumeColors(completeDataset.volume, completeDataset.ohlc);
          this.volumeSeries.setData(volumeWithColors);
        }
        
        console.log(`[TradingView Pattern] Single bar updated via setData() fallback`);
        
      } catch (fallbackError) {
        console.error('Fallback setData() also failed:', fallbackError);
        this.showError(`Failed to update single bar: ${error instanceof Error ? error.message : String(error)}`);
      }
    }
  }

  public getInfiniteScrollMetrics() {
    return this.dataManager?.getMetrics();
  }

  public getMemoryStats() {
    return this.dataManager?.getMemoryStats();
  }

  /**
   * Get memory status from memory monitor
   */
  public getMemoryStatus() {
    return this.memoryMonitor.getStatus();
  }

  /**
   * Handle data trimming notifications
   */
  private handleDataTrimming(originalCount: number, trimmedCount: number): void {
    // Notify user if data was trimmed
    console.info(`Data trimmed for performance: ${originalCount} → ${trimmedCount} bars`);
    
    // Show UI notification
    this.showStatus(`Memory optimized: displaying ${trimmedCount} of ${originalCount} bars`);
    
    // Could show more detailed UI notification in future
    this.showTrimNotification?.(originalCount, trimmedCount);
  }

  /**
   * Optional UI notification for data trimming (to be implemented by UI)
   */
  private showTrimNotification?: (originalCount: number, trimmedCount: number) => void;

  public updateInfiniteScrollConfig(config: Partial<InfiniteScrollConfig>): void {
    this.dataManager?.updateConfig(config);
  }

  /**
   * Get current datafeed data (TradingView pattern)
   */
  public getDatafeedData(): { ohlc: OHLCData[]; volume: VolumeData[] } | null {
    return this.datafeed?.getCurrentData() || null;
  }

  /**
   * Get datafeed statistics
   */
  public getDatafeedStats(): { size: number; earliest: number; latest: number } | null {
    if (!this.datafeed) return null;
    
    return {
      size: this.datafeed.getDatasetSize(),
      earliest: this.datafeed.getEarliestTimestamp(),
      latest: this.datafeed.getLatestTimestamp()
    };
  }

  /**
   * Get sliding window memory metrics for monitoring
   */
  public getSlidingWindowMetrics() {
    return this.datafeed?.getMemoryMetrics() || null;
  }

  /**
   * Get current sliding window state
   */
  public getSlidingWindowState() {
    return this.datafeed?.getWindowState() || null;
  }

  /**
   * Update sliding window configuration
   */
  public updateSlidingWindowConfig(config: Partial<SlidingWindowConfig>): void {
    if (this.datafeed) {
      this.datafeed.updateSlidingWindowConfig(config);
      console.log('[EnhancedChartManager] Sliding window configuration updated');
    }
  }

  public destroy(): void {
    // Cleanup datafeed
    if (this.datafeed) {
      this.datafeed.clear();
      this.datafeed = null;
    }

    // Cleanup data manager
    if (this.dataManager) {
      this.dataManager.destroy();
      this.dataManager = null;
    }

    // Cleanup charts
    if (this.chart) {
      this.chart.remove();
      this.chart = null;
    }
    if (this.volumeChart) {
      this.volumeChart.remove();
      this.volumeChart = null;
    }

    // Cleanup range event callbacks
    this.rangeEventCallbacks.clear();

    // Cleanup references
    this.candlestickSeries = null;
    this.volumeSeries = null;
    this.loadingIndicators.clear();

    // Remove loading indicators
    if (this.elements.infiniteScrollLoadingEl) {
      this.elements.infiniteScrollLoadingEl.remove();
    }
  }
}