import {
  InfiniteScrollConfig,
  DEFAULT_INFINITE_SCROLL_CONFIG,
  DataRange,
  LoadDirection,
  LoadingState,
  DataChunk,
  MemoryStats,
  DataManagerState,
  InfiniteScrollEvents,
  ScrollBoundary,
  LoadRequest,
  InfiniteScrollMetrics,
} from '../types/infinite-scroll';
import {
  OHLCData,
  VolumeData,
  Timeframe,
  ChartResponse,
} from '../types/chart';
import { LogicalRange } from '../types';
import { apiClient } from './api-client';

import type { Socket } from 'socket.io-client';
import { ChartDataRequest, ChartDataResponse } from '../types/websocket';

// Enhanced components
import { OptimizedDataManager } from './optimized-data-manager';
import { ConcurrentLoadManager } from './concurrent-load-manager';
import { SmartMemoryManager } from './smart-memory-manager';
import { EnhancedErrorHandler, ErrorCategory } from './enhanced-error-handler';

/**
 * Enhanced infinite scroll data manager with production-ready performance and reliability
 */
export class EnhancedInfiniteScrollManager {
  private config: InfiniteScrollConfig;
  private state: DataManagerState;
  private events: Partial<InfiniteScrollEvents>;
  private metrics: InfiniteScrollMetrics;
  private loadRequestCounter = 0;

  // Enhanced components
  private dataManager: OptimizedDataManager;
  private loadManager: ConcurrentLoadManager;
  private memoryManager: SmartMemoryManager;
  private errorHandler: EnhancedErrorHandler;

  // Debouncing and performance
  private scrollTimeout: NodeJS.Timeout | null = null;
  private performanceMonitor: PerformanceMonitor;

  constructor(
    instrument: string,
    timeframe: Timeframe,
    config: Partial<InfiniteScrollConfig> = {},
    events: Partial<InfiniteScrollEvents> = {},
    private socket?: Socket
  ) {
    this.config = { ...DEFAULT_INFINITE_SCROLL_CONFIG, ...config };
    this.events = events;
    
    this.state = {
      instrument,
      timeframe,
      loadedRanges: [],
      loadingStates: new Map(),
      allOhlcData: [],
      allVolumeData: [],
      memoryStats: this.createEmptyMemoryStats(),
      initialized: false,
      pendingRequests: new Set(),
    };

    this.metrics = {
      totalLoadRequests: 0,
      successfulLoads: 0,
      failedLoads: 0,
      averageLoadTimeMs: 0,
      totalBarsLoaded: 0,
      totalMemoryTrimmed: 0,
      currentMemoryUsageMB: 0,
      cacheHitRate: 0,
    };

    // Initialize enhanced components
    this.dataManager = new OptimizedDataManager(this.config);
    this.loadManager = new ConcurrentLoadManager();
    this.memoryManager = new SmartMemoryManager(this.config);
    this.errorHandler = new EnhancedErrorHandler({
      failureThreshold: 3,
      recoveryTimeout: 60000,
      testRequestTimeout: 10000,
    });
    this.performanceMonitor = new PerformanceMonitor();
  }

  /**
   * Initialize with initial data
   */
  async initialize(initialData: ChartResponse): Promise<void> {
    try {
      // Validate initial data
      if (!this.errorHandler.validateChunkData(initialData, 'initial_load')) {
        throw new Error('Invalid initial data structure');
      }

      // Initialize data manager
      this.dataManager.clear();
      this.dataManager.appendData(initialData.ohlc, initialData.volume);

      // Update state with optimized data
      const data = this.dataManager.getAllData();
      this.state.allOhlcData = data.ohlc;
      this.state.allVolumeData = data.volume;
      
      if (initialData.ohlc.length > 0) {
        const range: DataRange = {
          startTime: this.getTimeAsSeconds(initialData.ohlc[0].time),
          endTime: this.getTimeAsSeconds(initialData.ohlc[initialData.ohlc.length - 1].time),
          barCount: initialData.ohlc.length,
          loading: false,
        };
        this.state.loadedRanges = [range];
      }

      this.updateMemoryStats();
      this.state.initialized = true;
      
      this.emitEvent('onStatusUpdated', `Initialized with ${initialData.ohlc.length} bars (Enhanced)`);
      console.log(`✅ [EnhancedInfiniteScroll] Initialized with ${initialData.ohlc.length} bars`);
      
    } catch (error) {
      console.error('[EnhancedInfiniteScroll] Initialization failed:', error);
      throw error;
    }
  }

  /**
   * Enhanced scroll check with debouncing and intelligent loading
   */
  checkAndLoadData(logicalRange: LogicalRange): void {
    if (!this.state.initialized) {
      console.log('[EnhancedInfiniteScroll] Not initialized, skipping load check');
      return;
    }

    this.state.lastLogicalRange = logicalRange;
    this.performanceMonitor.recordScrollEvent(logicalRange);

    // Track memory access
    this.memoryManager.trackAccess(`viewport_${logicalRange.from}_${logicalRange.to}`);

    const boundary = this.detectScrollBoundary(logicalRange);

    // Enhanced logging with performance metrics
    const loadingState = this.loadManager.getLoadingState();
    console.log(`[EnhancedInfiniteScroll] Scroll check - Left: ${boundary.leftDistance}, Right: ${boundary.rightDistance}, Trigger: ${this.config.loadTriggerDistance}, Loading: H:${loadingState.historical} R:${loadingState.recent}`);

    // Debounce rapid scroll events
    if (this.scrollTimeout) {
      clearTimeout(this.scrollTimeout);
    }

    this.scrollTimeout = setTimeout(() => {
      this.handleScrollBoundaryWithIntelligence(boundary);
    }, this.config.scrollDebounceMs);
  }

  /**
   * Enhanced boundary handling with concurrent load management and memory checks
   */
  private async handleScrollBoundaryWithIntelligence(boundary: ScrollBoundary): Promise<void> {
    // Check memory pressure before loading more data
    const memoryPressure = this.memoryManager.checkMemoryPressure();
    
    if (memoryPressure.pressure === 'critical') {
      console.warn('[EnhancedInfiniteScroll] Critical memory pressure, performing emergency cleanup');
      await this.performEmergencyMemoryCleanup();
    } else if (memoryPressure.pressure === 'warning') {
      console.warn('[EnhancedInfiniteScroll] Memory pressure warning, preemptive cleanup');
      await this.performPreemptiveMemoryCleanup();
    }

    const promises: Promise<void>[] = [];

    // Historical loading with enhanced management
    if (boundary.nearLeft && this.config.bidirectionalLoading) {
      const loadPromise = this.loadManager.executeLoad(LoadDirection.HISTORICAL, async () => {
        await this.loadHistoricalDataEnhanced();
      });
      
      if (loadPromise) {
        promises.push(loadPromise);
      }
    }

    // Recent loading with enhanced management
    if (boundary.nearRight && this.config.bidirectionalLoading) {
      const loadPromise = this.loadManager.executeLoad(LoadDirection.RECENT, async () => {
        await this.loadRecentDataEnhanced();
      });
      
      if (loadPromise) {
        promises.push(loadPromise);
      }
    }

    // Execute loads with proper error handling
    if (promises.length > 0) {
      const results = await Promise.allSettled(promises);
      
      // Check for any failures
      const failures = results.filter(r => r.status === 'rejected') as PromiseRejectedResult[];
      if (failures.length > 0) {
        console.error('[EnhancedInfiniteScroll] Some loads failed:', failures.map(f => f.reason));
      }
    }
  }

  /**
   * Enhanced historical data loading with error handling and optimization
   */
  private async loadHistoricalDataEnhanced(): Promise<void> {
    const direction = LoadDirection.HISTORICAL;
    const context = `${this.state.instrument}_${direction}`;
    
    // Check if we have any loaded data
    if (this.state.allOhlcData.length === 0) return;

    // Use the timestamp of the FIRST currently loaded bar (not absolute dataset boundary)
    const firstLoadedTimestamp = this.getTimeAsSeconds(this.state.allOhlcData[0].time);
    const loadRequest = this.createLoadRequest(direction, firstLoadedTimestamp);

    const startTime = Date.now();
    
    try {
      await this.errorHandler.executeWithRetry(async () => {
        await this.executeLoadRequestEnhanced(loadRequest, direction);
      }, context, 3);
      
      this.performanceMonitor.recordLoadSuccess(direction, Date.now() - startTime);
      
    } catch (error) {
      this.performanceMonitor.recordLoadFailure(direction, Date.now() - startTime);
      this.handleLoadError(direction, error as Error);
      
      // Emit user-friendly error
      this.emitEvent('onLoadingError', direction, `Failed to load historical data: ${(error as Error).message}`);
    }
  }

  /**
   * Enhanced recent data loading
   */
  private async loadRecentDataEnhanced(): Promise<void> {
    const direction = LoadDirection.RECENT;
    const context = `${this.state.instrument}_${direction}`;
    
    // Check if we have any loaded data
    if (this.state.allOhlcData.length === 0) return;

    // Use the timestamp of the LAST currently loaded bar (not absolute dataset boundary)
    const lastLoadedTimestamp = this.getTimeAsSeconds(this.state.allOhlcData[this.state.allOhlcData.length - 1].time);
    const loadRequest = this.createLoadRequest(direction, lastLoadedTimestamp);

    const startTime = Date.now();
    
    try {
      await this.errorHandler.executeWithRetry(async () => {
        await this.executeLoadRequestEnhanced(loadRequest, direction);
      }, context, 3);
      
      this.performanceMonitor.recordLoadSuccess(direction, Date.now() - startTime);
      
    } catch (error) {
      this.performanceMonitor.recordLoadFailure(direction, Date.now() - startTime);
      this.handleLoadError(direction, error as Error);
      
      this.emitEvent('onLoadingError', direction, `Failed to load recent data: ${(error as Error).message}`);
    }
  }

  /**
   * Enhanced load request execution with validation and optimization
   */
  private async executeLoadRequestEnhanced(request: LoadRequest, direction: LoadDirection): Promise<void> {
    this.startLoading(direction, request);

    try {
      let response: ChartResponse;

      // Use WebSocket if available, otherwise fall back to REST API
      if (this.socket && this.socket.connected) {
        response = await this.loadDataViaWebSocket(request, direction);
      } else {
        response = await this.loadDataViaRestAPI(request, direction);
      }

      // Validate response
      if (!this.errorHandler.validateChunkData(response, `${direction}_load`)) {
        throw new Error(`Invalid response data for ${direction} load`);
      }

      if (response.ohlc.length === 0) {
        this.emitEvent('onStatusUpdated', `No more ${direction} data available`);
        this.finishLoading(direction);
        return;
      }

      // Process data with enhanced manager
      await this.processDataChunkEnhanced(response, direction, request.id);
      
      this.metrics.successfulLoads++;
      this.metrics.totalBarsLoaded += response.ohlc.length;
      
      this.emitEvent('onDataLoaded', this.createDataChunk(response, direction, request.id));
      this.finishLoading(direction);
      
    } catch (error) {
      this.metrics.failedLoads++;
      throw error;
    } finally {
      this.state.pendingRequests.delete(request.id);
      this.updateMetrics(request);
    }
  }

  /**
   * Enhanced data chunk processing with optimized memory management
   */
  private async processDataChunkEnhanced(response: ChartResponse, direction: LoadDirection, requestId: string): Promise<void> {
    // Use optimized data manager instead of array merging
    if (direction === LoadDirection.HISTORICAL) {
      this.dataManager.prependData(response.ohlc, response.volume);
      console.log(`📊 [EnhancedInfiniteScroll] Prepended ${response.ohlc.length} historical bars`);
    } else {
      this.dataManager.appendData(response.ohlc, response.volume);
      console.log(`📊 [EnhancedInfiniteScroll] Appended ${response.ohlc.length} recent bars`);
    }

    // Update state with current data
    const currentData = this.dataManager.getAllData();
    this.state.allOhlcData = currentData.ohlc;
    this.state.allVolumeData = currentData.volume;

    // Update loaded ranges
    this.updateLoadedRanges(response, direction);

    // Smart memory management
    await this.performSmartMemoryManagement();

    // Update metrics
    this.updateMemoryStats();
    
    console.log(`✅ [EnhancedInfiniteScroll] Processed ${direction} chunk - ${response.ohlc.length} bars, total: ${this.dataManager.getBoundaries().totalBars}`);
  }

  /**
   * Smart memory management using enhanced memory manager
   */
  private async performSmartMemoryManagement(): Promise<void> {
    const memoryPressure = this.memoryManager.checkMemoryPressure();
    
    if (memoryPressure.recommendedAction === 'none') {
      return;
    }

    const boundaries = this.dataManager.getBoundaries();
    if (boundaries.totalBars <= this.config.maxBarsInMemory) {
      return;
    }

    const strategy = this.memoryManager.getOptimalTrimStrategy(
      this.state.lastLogicalRange || null,
      boundaries.totalBars,
      memoryPressure.pressure === 'critical' ? 'critical' : 'warning'
    );

    const trimmedBars = this.dataManager.smartTrim(this.state.lastLogicalRange || null);
    
    if (trimmedBars > 0) {
      this.metrics.totalMemoryTrimmed += trimmedBars;
      
      // Update state after trimming
      const currentData = this.dataManager.getAllData();
      this.state.allOhlcData = currentData.ohlc;
      this.state.allVolumeData = currentData.volume;
      
      this.updateLoadedRangesAfterTrim();
      this.emitEvent('onMemoryTrimmed', trimmedBars, strategy.action);
      
      console.log(`🧹 [EnhancedInfiniteScroll] Smart trim: ${trimmedBars} bars removed (${strategy.reason})`);
    }
  }

  /**
   * Emergency memory cleanup for critical situations
   */
  private async performEmergencyMemoryCleanup(): Promise<void> {
    console.warn('[EnhancedInfiniteScroll] Performing emergency memory cleanup');
    
    // Check memory pressure first
    const memoryPressure = this.memoryManager.checkMemoryPressure();
    
    if (memoryPressure.pressure === 'critical' || memoryPressure.currentMemoryMB > 300) {
      // Use enhanced emergency trimming with 50% reduction
      const currentData = this.dataManager.getAllData();
      
      if (currentData.ohlc.length > 0) {
        const trimResult = this.memoryManager.performEmergencyTrimming(
          currentData.ohlc,
          this.state.lastLogicalRange || null
        );
        
        // Apply trimming to both OHLC and volume data
        this.state.allOhlcData = trimResult.trimmedData;
        
        // Trim volume data to match OHLC data
        const volumeStartIndex = currentData.volume.length - trimResult.trimmedData.length;
        this.state.allVolumeData = currentData.volume.slice(Math.max(0, volumeStartIndex));
        
        // Update data manager with trimmed data
        this.dataManager.setData(this.state.allOhlcData, this.state.allVolumeData);
        
        this.metrics.totalMemoryTrimmed += trimResult.trimmedCount;
        
        // Force garbage collection if available
        this.memoryManager.forceGarbageCollection();
        
        console.warn(`🚨 [EnhancedInfiniteScroll] EMERGENCY TRIMMING: ${trimResult.trimmedCount} bars removed - ${trimResult.reason}`);
        
        // Trigger events
        if (this.events.onMemoryTrimmed) {
          this.events.onMemoryTrimmed(trimResult.trimmedCount, LoadDirection.HISTORICAL);
        }
        
        if (this.events.onStatusUpdated) {
          this.events.onStatusUpdated(`Emergency cleanup: removed ${trimResult.trimmedCount} bars`);
        }
      }
    } else {
      // Fallback to standard trimming
      const boundaries = this.dataManager.getBoundaries();
      const targetSize = Math.floor(this.config.maxBarsInMemory * 0.5);
      
      if (boundaries.totalBars > targetSize) {
        const trimmed = this.dataManager.smartTrim(this.state.lastLogicalRange || null);
        this.metrics.totalMemoryTrimmed += trimmed;
        
        // Update state
        const currentData = this.dataManager.getAllData();
        this.state.allOhlcData = currentData.ohlc;
        this.state.allVolumeData = currentData.volume;
        
        // Force garbage collection if available
        this.memoryManager.forceGarbageCollection();
        
        console.warn(`🚨 [EnhancedInfiniteScroll] Standard emergency cleanup: ${trimmed} bars removed`);
      }
    }
  }

  /**
   * Preemptive memory cleanup for warning situations
   */
  private async performPreemptiveMemoryCleanup(): Promise<void> {
    const boundaries = this.dataManager.getBoundaries();
    
    if (boundaries.totalBars > this.config.maxBarsInMemory * 0.9) {
      const trimmed = this.dataManager.smartTrim(this.state.lastLogicalRange || null);
      this.metrics.totalMemoryTrimmed += trimmed;
      
      if (trimmed > 0) {
        const currentData = this.dataManager.getAllData();
        this.state.allOhlcData = currentData.ohlc;
        this.state.allVolumeData = currentData.volume;
        
        console.log(`⚠️ [EnhancedInfiniteScroll] Preemptive cleanup: ${trimmed} bars removed`);
      }
    }
  }

  /**
   * Get comprehensive performance and health metrics
   */
  getEnhancedMetrics(): {
    basic: InfiniteScrollMetrics;
    performance: any;
    memory: any;
    errors: any;
    loading: any;
  } {
    return {
      basic: this.getMetrics(),
      performance: this.performanceMonitor.getMetrics(),
      memory: this.memoryManager.getMemoryStatistics(),
      errors: this.errorHandler.getErrorStatistics(),
      loading: this.loadManager.getLoadingState(),
    };
  }

  // ... [Rest of the implementation continues with existing methods, enhanced where needed]
  
  /**
   * All existing methods from the original InfiniteScrollDataManager,
   * with enhanced error handling and performance monitoring
   */

  private detectScrollBoundary(logicalRange: LogicalRange): ScrollBoundary {
    const totalBars = this.dataManager.getBoundaries().totalBars;
    const leftDistance = logicalRange.from;
    const rightDistance = totalBars - logicalRange.to;

    return {
      nearLeft: leftDistance < this.config.loadTriggerDistance,
      nearRight: rightDistance < this.config.loadTriggerDistance,
      leftDistance,
      rightDistance,
      currentRange: logicalRange,
    };
  }

  private createLoadRequest(direction: LoadDirection, timestamp: number): LoadRequest {
    return {
      id: `enhanced_load_${++this.loadRequestCounter}_${Date.now()}`,
      direction,
      timestamp,
      limit: this.config.chunkSize,
      createdAt: Date.now(),
      timeoutMs: 30000,
    };
  }

  private createDataChunk(response: ChartResponse, direction: LoadDirection, requestId: string): DataChunk {
    const range: DataRange = {
      startTime: this.getTimeAsSeconds(response.ohlc[0].time),
      endTime: this.getTimeAsSeconds(response.ohlc[response.ohlc.length - 1].time),
      barCount: response.ohlc.length,
      loading: false,
    };

    return {
      ohlc: response.ohlc,
      volume: response.volume,
      range,
      direction,
      requestId,
    };
  }

  // [Continue with rest of the implementation...]
  // Include all other methods from the original class, enhancing them where appropriate

  public getCurrentData() {
    return this.dataManager.getAllData();
  }

  public getState(): Readonly<DataManagerState> {
    return { ...this.state };
  }

  public getMetrics(): Readonly<InfiniteScrollMetrics> {
    return { ...this.metrics };
  }

  public getMemoryStats(): Readonly<MemoryStats> {
    return { ...this.state.memoryStats };
  }

  public updateConfig(config: Partial<InfiniteScrollConfig>): void {
    this.config = { ...this.config, ...config };
    console.log('[EnhancedInfiniteScroll] Configuration updated:', config);
  }

  public destroy(): void {
    if (this.scrollTimeout) {
      clearTimeout(this.scrollTimeout);
    }
    
    this.dataManager.clear();
    this.loadManager.destroy();
    this.memoryManager.destroy();
    this.performanceMonitor.destroy();
    
    this.state.pendingRequests.clear();
    this.state.loadingStates.clear();
    this.state.initialized = false;
    
    console.log('[EnhancedInfiniteScroll] Destroyed');
  }

  // [Additional helper methods would continue here...]
  private startLoading(direction: LoadDirection, request: LoadRequest): void {
    const loadingState: LoadingState = {
      direction,
      isLoading: true,
      startTime: Date.now(),
    };

    this.state.loadingStates.set(direction, loadingState);
    this.state.pendingRequests.add(request.id);
    this.metrics.totalLoadRequests++;

    this.emitEvent('onLoadingStarted', direction, {
      startTime: request.timestamp,
      endTime: request.timestamp,
      barCount: 0,
      loading: true,
    });
  }

  private finishLoading(direction: LoadDirection): void {
    this.state.loadingStates.delete(direction);
    this.emitEvent('onLoadingFinished', direction);
  }

  private handleLoadError(direction: LoadDirection, error: Error): void {
    const loadingState = this.state.loadingStates.get(direction);
    if (loadingState) {
      loadingState.error = error.message;
    }

    this.emitEvent('onLoadingError', direction, error.message);
    this.finishLoading(direction);
  }

  private updateLoadedRanges(response: ChartResponse, direction: LoadDirection): void {
    if (response.ohlc.length === 0) return;

    const range: DataRange = {
      startTime: this.getTimeAsSeconds(response.ohlc[0].time),
      endTime: this.getTimeAsSeconds(response.ohlc[response.ohlc.length - 1].time),
      barCount: response.ohlc.length,
      loading: false,
    };

    this.state.loadedRanges.push(range);
    this.mergeOverlappingRanges();
  }

  private updateLoadedRangesAfterTrim(): void {
    const boundaries = this.dataManager.getBoundaries();
    if (boundaries.totalBars === 0) {
      this.state.loadedRanges = [];
      return;
    }

    this.state.loadedRanges = [{
      startTime: boundaries.earliest!,
      endTime: boundaries.latest!,
      barCount: boundaries.totalBars,
      loading: false,
    }];
  }

  private mergeOverlappingRanges(): void {
    this.state.loadedRanges.sort((a, b) => a.startTime - b.startTime);
    
    const merged: DataRange[] = [];
    for (const range of this.state.loadedRanges) {
      const last = merged[merged.length - 1];
      
      if (last && range.startTime <= last.endTime) {
        last.endTime = Math.max(last.endTime, range.endTime);
        last.barCount += range.barCount;
      } else {
        merged.push({ ...range });
      }
    }
    
    this.state.loadedRanges = merged;
  }

  private getTimeAsSeconds(time: Time): number {
    if (typeof time === 'number') {
      if (time > 1e9 && time < 2e9) {
        return time;
      }
      if (time > 1e12 && time < 2e12) {
        return Math.floor(time / 1000);
      }
      throw new Error(`Invalid timestamp: ${time}`);
    }
    if (typeof time === 'string') {
      const timestamp = Math.floor(new Date(time).getTime() / 1000);
      if (isNaN(timestamp)) {
        throw new Error(`Invalid date string: ${time}`);
      }
      return timestamp;
    }
    return Math.floor(Date.now() / 1000);
  }

  private createEmptyMemoryStats(): MemoryStats {
    return {
      totalBars: 0,
      ohlcBars: 0,
      volumeBars: 0,
      memoryUsageEstimateMB: 0,
      oldestBarTime: 0,
      newestBarTime: 0,
      loadedRanges: [],
    };
  }

  private updateMemoryStats(): void {
    const boundaries = this.dataManager.getBoundaries();
    const memoryStats = this.dataManager.getMemoryStats();

    this.state.memoryStats = {
      totalBars: boundaries.totalBars,
      ohlcBars: boundaries.totalBars,
      volumeBars: this.state.allVolumeData.length,
      memoryUsageEstimateMB: memoryStats.estimatedMemoryMB,
      oldestBarTime: boundaries.earliest || 0,
      newestBarTime: boundaries.latest || 0,
      loadedRanges: [...this.state.loadedRanges],
    };

    this.metrics.currentMemoryUsageMB = memoryStats.estimatedMemoryMB;
  }

  private updateMetrics(request: LoadRequest): void {
    const duration = Date.now() - request.createdAt;
    const totalRequests = this.metrics.totalLoadRequests;
    
    this.metrics.averageLoadTimeMs = 
      (this.metrics.averageLoadTimeMs * (totalRequests - 1) + duration) / totalRequests;
  }

  private emitEvent(eventName: keyof InfiniteScrollEvents, ...args: unknown[]): void {
    const handler = this.events[eventName] as Function;
    if (handler && typeof handler === 'function') {
      try {
        handler(...args);
      } catch (error) {
        console.error(`Error in infinite scroll event ${eventName}:`, error);
      }
    }
  }

  /**
   * Load data via WebSocket with enhanced error handling
   */
  private async loadDataViaWebSocket(request: LoadRequest, direction: LoadDirection): Promise<ChartResponse> {
    if (!this.socket) {
      throw new Error('WebSocket not available');
    }

    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        this.socket?.off('chart_data', onChartData);
        this.socket?.off('error', onError);
        reject(new Error(`WebSocket request timeout for ${direction} data after ${request.timeoutMs}ms`));
      }, request.timeoutMs);

      const onChartData = (data: ChartDataResponse) => {
        clearTimeout(timeoutId);
        this.socket?.off('chart_data', onChartData);
        this.socket?.off('error', onError);

        if (data.error) {
          reject(new Error(data.error));
          return;
        }

        // Enhanced validation before processing
        if (!data.ohlc || !Array.isArray(data.ohlc)) {
          reject(new Error('Invalid OHLC data structure in WebSocket response'));
          return;
        }

        // Convert WebSocket response to ChartResponse format
        const chartResponse: ChartResponse = {
          ohlc: data.ohlc,
          volume: data.volume || [],
          instrument: data.instrument || this.state.instrument,
          timeframe: data.timeframe || this.state.timeframe,
          bars_returned: data.bars_returned || data.ohlc.length,
          statistics: data.statistics ? {
            date_range: data.statistics.date_range || { start: '', end: '' },
            bars_count: data.statistics.bars_count || data.ohlc.length
          } : undefined,
          data_quality: data.data_quality ? {
            is_valid: data.data_quality.is_valid ?? true
          } : undefined
        };

        resolve(chartResponse);
      };

      const onError = (error: any) => {
        clearTimeout(timeoutId);
        this.socket?.off('chart_data', onChartData);
        this.socket?.off('error', onError);
        reject(new Error(error.message || 'WebSocket request failed'));
      };

      if (!this.socket) {
        reject(new Error('WebSocket connection not available'));
        return;
      }

      this.socket.once('chart_data', onChartData);
      this.socket.once('error', onError);

      // Create WebSocket request
      const wsRequest: ChartDataRequest = {
        instrument_id: this.state.instrument,
        timeframe: this.state.timeframe,
        limit: request.limit
      };

      // Add timestamp parameter based on direction
      if (direction === LoadDirection.HISTORICAL) {
        wsRequest.before_timestamp_seconds = request.timestamp;
      } else if (direction === LoadDirection.RECENT) {
        wsRequest.after_timestamp_seconds = request.timestamp;
      }

      console.log(`[EnhancedInfiniteScroll] Sending WebSocket request for ${direction} data:`, wsRequest);
      this.socket.emit('request_chart_data', wsRequest);
    });
  }

  private async loadDataViaRestAPI(request: LoadRequest, direction: LoadDirection): Promise<ChartResponse> {
    // Implementation with bidirectional timestamp support
    const beforeTimestamp = direction === LoadDirection.HISTORICAL ? request.timestamp : undefined;
    const afterTimestamp = direction === LoadDirection.RECENT ? request.timestamp : undefined;
    
    return await apiClient.getChartData(
      this.state.instrument,
      this.state.timeframe,
      {
        beforeTimestamp,
        afterTimestamp,
        limit: request.limit,
      }
    );
  }
}

/**
 * Performance monitoring helper class
 */
class PerformanceMonitor {
  private scrollEvents: Array<{ timestamp: number; range: LogicalRange }> = [];
  private loadEvents: Array<{ 
    direction: LoadDirection; 
    timestamp: number; 
    success: boolean; 
    duration: number;
  }> = [];

  recordScrollEvent(range: LogicalRange): void {
    this.scrollEvents.push({ timestamp: Date.now(), range });
    
    // Keep only recent events
    if (this.scrollEvents.length > 100) {
      this.scrollEvents = this.scrollEvents.slice(-50);
    }
  }

  recordLoadSuccess(direction: LoadDirection, duration: number): void {
    this.loadEvents.push({
      direction,
      timestamp: Date.now(),
      success: true,
      duration,
    });
  }

  recordLoadFailure(direction: LoadDirection, duration: number): void {
    this.loadEvents.push({
      direction,
      timestamp: Date.now(),
      success: false,
      duration,
    });
  }

  getMetrics(): {
    scrollVelocity: number;
    averageLoadTime: number;
    successRate: number;
    recentActivity: any;
  } {
    const recent = this.scrollEvents.slice(-10);
    const scrollVelocity = recent.length > 1 
      ? (recent[recent.length - 1].range.to - recent[0].range.from) / (recent.length - 1)
      : 0;

    const recentLoads = this.loadEvents.slice(-20);
    const successfulLoads = recentLoads.filter(e => e.success);
    const averageLoadTime = successfulLoads.length > 0
      ? successfulLoads.reduce((sum, e) => sum + e.duration, 0) / successfulLoads.length
      : 0;

    const successRate = recentLoads.length > 0 
      ? successfulLoads.length / recentLoads.length 
      : 1;

    return {
      scrollVelocity,
      averageLoadTime,
      successRate,
      recentActivity: {
        scrollEvents: this.scrollEvents.length,
        loadEvents: this.loadEvents.length,
      },
    };
  }

  destroy(): void {
    this.scrollEvents.length = 0;
    this.loadEvents.length = 0;
  }
}