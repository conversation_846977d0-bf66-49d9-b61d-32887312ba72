import { OHLCData, VolumeData } from '../types/chart';
import { LogicalRange } from '../types';

import { InfiniteScrollConfig } from '../types/infinite-scroll';

/**
 * Optimized data manager that prevents inefficient array merging
 * and provides efficient data operations for infinite scrolling
 */
export class OptimizedDataManager {
    private ohlcData: OHLCData[] = [];
    private volumeData: VolumeData[] = [];
    private boundaries: {
        earliest: number | null;
        latest: number | null;
    } = { earliest: null, latest: null };
    
    private readonly maxPoints: number;
    private readonly trimSize: number;

    constructor(config: InfiniteScrollConfig) {
        this.maxPoints = config.maxBarsInMemory;
        this.trimSize = config.trimSize || Math.floor(config.maxBarsInMemory * 0.1);
    }

    /**
     * Efficiently prepend historical data without array recreation
     */
    prependData(newOhlc: OHLCData[], newVolume: VolumeData[]): void {
        if (newOhlc.length === 0) return;

        // Use unshift for small chunks, splice for larger ones
        if (newOhlc.length < 1000) {
            this.ohlcData.unshift(...newOhlc);
            this.volumeData.unshift(...newVolume);
        } else {
            // For larger chunks, use splice which is more efficient
            this.ohlcData.splice(0, 0, ...newOhlc);
            this.volumeData.splice(0, 0, ...newVolume);
        }

        this.updateBoundaries();
        this.checkMemoryLimits();
    }

    /**
     * Efficiently append recent data
     */
    appendData(newOhlc: OHLCData[], newVolume: VolumeData[]): void {
        if (newOhlc.length === 0) return;

        // Simple push is most efficient for appending
        this.ohlcData.push(...newOhlc);
        this.volumeData.push(...newVolume);

        this.updateBoundaries();
        this.checkMemoryLimits();
    }

    /**
     * Update single bar (for real-time updates)
     */
    updateLatestBar(ohlc: OHLCData, volume?: VolumeData): void {
        if (this.ohlcData.length === 0) return;

        const lastIndex = this.ohlcData.length - 1;
        this.ohlcData[lastIndex] = ohlc;
        
        if (volume && this.volumeData.length > lastIndex) {
            this.volumeData[lastIndex] = volume;
        }

        this.updateBoundaries();
    }

    /**
     * Add new bar (for real-time updates)
     */
    addNewBar(ohlc: OHLCData, volume?: VolumeData): void {
        this.ohlcData.push(ohlc);
        if (volume) {
            this.volumeData.push(volume);
        }

        this.updateBoundaries();
        this.checkMemoryLimits();
    }

    /**
     * Smart memory trimming that preserves user's current viewport
     */
    smartTrim(currentViewport: LogicalRange | null): number {
        if (this.ohlcData.length <= this.maxPoints) {
            return 0;
        }

        const excessBars = this.ohlcData.length - this.maxPoints;
        const barsToTrim = Math.max(excessBars, this.trimSize);

        if (!currentViewport) {
            // No viewport info, trim from beginning (oldest data)
            return this.trimFromBeginning(barsToTrim);
        }

        // Calculate viewport position
        const totalBars = this.ohlcData.length;
        const viewportCenter = (currentViewport.from + currentViewport.to) / 2;
        const viewportRatio = viewportCenter / totalBars;

        if (viewportRatio < 0.3) {
            // User is viewing older data, trim from end
            return this.trimFromEnd(barsToTrim);
        } else if (viewportRatio > 0.7) {
            // User is viewing newer data, trim from beginning
            return this.trimFromBeginning(barsToTrim);
        } else {
            // User is in middle, preserve viewport with buffer
            return this.trimAroundViewport(currentViewport, barsToTrim);
        }
    }

    /**
     * Get all data (creates defensive copy only when needed)
     */
    getAllData(): { ohlc: OHLCData[], volume: VolumeData[] } {
        return {
            ohlc: this.ohlcData,
            volume: this.volumeData
        };
    }

    /**
     * Get data slice without copying entire array
     */
    getDataSlice(start: number, end: number): { ohlc: OHLCData[], volume: VolumeData[] } {
        return {
            ohlc: this.ohlcData.slice(start, end),
            volume: this.volumeData.slice(start, end)
        };
    }

    /**
     * Get data boundaries
     */
    getBoundaries(): { earliest: number | null, latest: number | null, totalBars: number } {
        return {
            ...this.boundaries,
            totalBars: this.ohlcData.length
        };
    }

    /**
     * Clear all data
     */
    clear(): void {
        this.ohlcData.length = 0;
        this.volumeData.length = 0;
        this.boundaries = { earliest: null, latest: null };
    }

    /**
     * Get memory statistics
     */
    getMemoryStats(): {
        totalBars: number;
        estimatedMemoryMB: number;
        trimmedBars: number;
        efficiency: number;
    } {
        const totalBars = this.ohlcData.length;
        // Rough estimate: 100 bytes per OHLC bar, 50 bytes per volume bar
        const estimatedBytes = totalBars * 100 + this.volumeData.length * 50;
        const estimatedMB = estimatedBytes / (1024 * 1024);
        
        return {
            totalBars,
            estimatedMemoryMB: estimatedMB,
            trimmedBars: 0, // Will be tracked by caller
            efficiency: totalBars > 0 ? Math.min(1, this.maxPoints / totalBars) : 1
        };
    }

    private updateBoundaries(): void {
        if (this.ohlcData.length === 0) {
            this.boundaries = { earliest: null, latest: null };
            return;
        }

        this.boundaries.earliest = this.getTimeAsSeconds(this.ohlcData[0].time);
        this.boundaries.latest = this.getTimeAsSeconds(this.ohlcData[this.ohlcData.length - 1].time);
    }

    private checkMemoryLimits(): void {
        if (this.ohlcData.length > this.maxPoints * 1.2) {
            console.warn(`[OptimizedDataManager] Memory limit exceeded: ${this.ohlcData.length} bars > ${this.maxPoints * 1.2}`);
            // Force immediate trim
            this.smartTrim(null);
        }
    }

    private trimFromBeginning(barsToTrim: number): number {
        const actualTrim = Math.min(barsToTrim, this.ohlcData.length);
        
        this.ohlcData.splice(0, actualTrim);
        this.volumeData.splice(0, actualTrim);
        
        this.updateBoundaries();
        return actualTrim;
    }

    private trimFromEnd(barsToTrim: number): number {
        const actualTrim = Math.min(barsToTrim, this.ohlcData.length);
        
        this.ohlcData.splice(-actualTrim, actualTrim);
        this.volumeData.splice(-actualTrim, actualTrim);
        
        this.updateBoundaries();
        return actualTrim;
    }

    private trimAroundViewport(viewport: LogicalRange, barsToTrim: number): number {
        const totalBars = this.ohlcData.length;
        const bufferSize = Math.max(500, (viewport.to - viewport.from) * 2);
        
        // Calculate preservation range
        const preserveStart = Math.max(0, viewport.from - bufferSize);
        const preserveEnd = Math.min(totalBars, viewport.to + bufferSize);
        
        // Trim from both ends to reach target size
        const preserveLength = preserveEnd - preserveStart;
        if (preserveLength >= this.maxPoints) {
            // Just preserve the viewport area
            this.ohlcData = this.ohlcData.slice(preserveStart, preserveStart + this.maxPoints);
            this.volumeData = this.volumeData.slice(preserveStart, preserveStart + this.maxPoints);
            
            this.updateBoundaries();
            return totalBars - this.maxPoints;
        }

        return 0; // No trimming needed
    }

    private getTimeAsSeconds(time: Time): number {
        if (typeof time === 'number') {
            return time;
        }
        if (typeof time === 'string') {
            return Math.floor(new Date(time).getTime() / 1000);
        }
        return Math.floor(Date.now() / 1000);
    }
}