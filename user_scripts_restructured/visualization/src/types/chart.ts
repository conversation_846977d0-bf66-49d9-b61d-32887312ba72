

// Chart data types that match lightweight-charts format
export interface OHLCData {
  time: Time;
  open: number;
  high: number;
  low: number;
  close: number;
}

export interface VolumeData extends HistogramData {
  time: Time;
  value: number;
  color?: string;
}

export interface ChartStatistics {
  date_range: {
    start: string;
    end: string;
  };
  bars_count: number;
  data_gaps?: Array<{
    start: string;
    end: string;
    duration: string;
  }>;
}

// Import official data quality schema
import type { BaseDataQuality } from './data-quality';

// Use official data quality interface instead of local definition
export type DataQuality = BaseDataQuality;

export interface ChartResponse {
  ohlc: OHLCData[];
  volume: VolumeData[];
  instrument: string;
  timeframe: string;
  bars_returned: number;
  statistics?: ChartStatistics;
  data_quality?: DataQuality;
  message?: string;
  memory_status?: {
    memory_utilization: number;
    data_points: number;
    is_near_limit?: boolean;
  };
  data_points?: number;
}

// Chart configuration types that extend lightweight-charts
export type ChartOptions = DeepPartial<TimeChartOptions>;

// Series configuration types that extend lightweight-charts
export type CandlestickSeriesOptions = DeepPartial<CandlestickStyleOptions & SeriesOptionsCommon>;
export type VolumeSeriesOptions = DeepPartial<HistogramStyleOptions & SeriesOptionsCommon>;

// Chart state types
export interface ChartState {
  instrument: string;
  timeframe: Timeframe;
  isLoading: boolean;
  error: string | null;
  data: ChartResponse | null;
  lastUpdate: number;
}

// Timeframe types
export type Timeframe = '1min' | '5min' | '15min' | '30min' | '1h' | '4h' | '1d';

export interface TimeframeButton {
  timeframe: Timeframe;
  label: string;
  active: boolean;
}