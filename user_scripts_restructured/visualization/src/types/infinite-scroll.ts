// Vue-ECharts infinite scroll types (no TradingView dependency)
type Time = string | number;
type LogicalRange = { from: number; to: number; };
import { OHLCData, VolumeData, Timeframe } from './chart';

// Sliding window configuration for memory management
export interface SlidingWindowConfig {
  /** Maximum number of bars to keep in memory */
  MAX_BARS_IN_MEMORY: number;
  /** Number of bars to load in each chunk */
  CHUNK_SIZE: number;
  /** Threshold before triggering trim (bars over limit) */
  TRIM_THRESHOLD: number;
  /** Ratio of bars to trim from left side when over limit (0.0-1.0) */
  LEFT_TRIM_RATIO: number;
  /** Ratio of bars to trim from right side when over limit (0.0-1.0) */
  RIGHT_TRIM_RATIO: number;
  /** Enable aggressive memory cleanup */
  ENABLE_EMERGENCY_CLEANUP: boolean;
  /** Memory pressure threshold in MB */
  MEMORY_PRESSURE_THRESHOLD_MB: number;
}

// Default sliding window configuration
export const DEFAULT_SLIDING_WINDOW_CONFIG: SlidingWindowConfig = {
  MAX_BARS_IN_MEMORY: 5000,       // 5k bars maximum for emergency memory stability
  CHUNK_SIZE: 500,                // 500 bars per load
  TRIM_THRESHOLD: 1000,           // Trim when 1k bars over limit
  LEFT_TRIM_RATIO: 0.3,           // Trim 30% from left when loading right
  RIGHT_TRIM_RATIO: 0.3,          // Trim 30% from right when loading left
  ENABLE_EMERGENCY_CLEANUP: true, // Allow emergency cleanup
  MEMORY_PRESSURE_THRESHOLD_MB: 50, // Emergency cleanup at 50MB
};

// Memory pressure levels
export enum MemoryPressureLevel {
  LOW = 'low',
  MODERATE = 'moderate', 
  HIGH = 'high',
  CRITICAL = 'critical',
}

// Memory management strategy
export interface MemoryManagementStrategy {
  /** When to trim data */
  triggerLevel: MemoryPressureLevel;
  /** How much to trim */
  trimAmount: number;
  /** Which direction to trim from */
  trimDirection: LoadDirection;
  /** Whether to use update() method */
  useUpdateMethod: boolean;
}

// Sliding window state
export interface SlidingWindowState {
  /** Current window size */
  currentSize: number;
  /** Memory pressure level */
  pressureLevel: MemoryPressureLevel;
  /** Whether trimming is in progress */
  trimInProgress: boolean;
  /** Last trim operation timestamp */
  lastTrimTimestamp: number;
  /** Number of bars trimmed in last operation */
  lastTrimAmount: number;
  /** Direction of last trim */
  lastTrimDirection?: LoadDirection;
}

// Infinite scrolling configuration
export interface InfiniteScrollConfig {
  /** How close to the edge (in logical units) before triggering load */
  loadTriggerDistance: number;
  /** How many bars to load in each chunk */
  chunkSize: number;
  /** Maximum number of bars to keep in memory */
  maxBarsInMemory: number;
  /** How many bars to trim when memory limit exceeded */
  trimSize: number;
  /** Enable loading in both directions */
  bidirectionalLoading: boolean;
  /** Debounce time for scroll events (ms) */
  scrollDebounceMs: number;
  /** Enable memory management */
  enableMemoryManagement: boolean;
  /** Show loading indicators */
  showLoadingIndicators: boolean;
  /** Memory threshold in MB for warning */
  memoryThresholdMB?: number;
}

// Default configuration
export const DEFAULT_INFINITE_SCROLL_CONFIG: InfiniteScrollConfig = {
  loadTriggerDistance: 5, // Very low for testing - triggers when within 5 bars of edge
  chunkSize: 500,
  maxBarsInMemory: 5000,
  trimSize: 1000,
  bidirectionalLoading: true,
  scrollDebounceMs: 50, // Reduced debounce for faster response
  enableMemoryManagement: true,
  showLoadingIndicators: true,
  memoryThresholdMB: 200,
};

// Data range tracking
export interface DataRange {
  /** Start timestamp (Unix seconds) */
  startTime: number;
  /** End timestamp (Unix seconds) */
  endTime: number;
  /** Number of bars in this range */
  barCount: number;
  /** Whether this range is currently being loaded */
  loading: boolean;
  /** Loading request timestamp */
  loadingStarted?: number;
  /** Error message if loading failed */
  error?: string;
}

// Loading direction
export enum LoadDirection {
  HISTORICAL = 'historical',  // Loading older data (left side)
  RECENT = 'recent',          // Loading newer data (right side)
}

// Loading state
export interface LoadingState {
  direction: LoadDirection;
  isLoading: boolean;
  startTime: number;
  error?: string;
  progress?: number;
}

// Data chunk for loading
export interface DataChunk {
  ohlc: OHLCData[];
  volume: VolumeData[];
  range: DataRange;
  direction: LoadDirection;
  requestId: string;
}

// Memory management stats
export interface MemoryStats {
  totalBars: number;
  ohlcBars: number;
  volumeBars: number;
  memoryUsageEstimateMB: number;
  oldestBarTime: number;
  newestBarTime: number;
  loadedRanges: DataRange[];
}

// Data manager state
export interface DataManagerState {
  /** Current instrument */
  instrument: string;
  /** Current timeframe */
  timeframe: Timeframe;
  /** All loaded data ranges */
  loadedRanges: DataRange[];
  /** Current loading states */
  loadingStates: Map<LoadDirection, LoadingState>;
  /** Combined OHLC data */
  allOhlcData: OHLCData[];
  /** Combined volume data */
  allVolumeData: VolumeData[];
  /** Memory management stats */
  memoryStats: MemoryStats;
  /** Whether data manager is initialized */
  initialized: boolean;
  /** Last logical range received */
  lastLogicalRange?: LogicalRange;
  /** Pending load requests */
  pendingRequests: Set<string>;
}

// Events for infinite scrolling
export interface InfiniteScrollEvents {
  /** Data loaded successfully */
  onDataLoaded: (chunk: DataChunk) => void;
  /** Loading started */
  onLoadingStarted: (direction: LoadDirection, range: DataRange) => void;
  /** Loading finished */
  onLoadingFinished: (direction: LoadDirection) => void;
  /** Loading failed */
  onLoadingError: (direction: LoadDirection, error: string) => void;
  /** Memory trimmed */
  onMemoryTrimmed: (trimmedBars: number, direction: LoadDirection) => void;
  /** Status updated */
  onStatusUpdated: (status: string) => void;
}

// Scroll boundary detection
export interface ScrollBoundary {
  /** Whether near left boundary (historical data) */
  nearLeft: boolean;
  /** Whether near right boundary (recent data) */
  nearRight: boolean;
  /** Distance from left boundary */
  leftDistance: number;
  /** Distance from right boundary */
  rightDistance: number;
  /** Current logical range */
  currentRange: LogicalRange;
}

// Data loading request
export interface LoadRequest {
  /** Unique request ID */
  id: string;
  /** Loading direction */
  direction: LoadDirection;
  /** Timestamp to load from/to */
  timestamp: number;
  /** Number of bars to load */
  limit: number;
  /** When request was created */
  createdAt: number;
  /** Request timeout (ms) */
  timeoutMs: number;
}

// WebSocket infinite scroll request options
export interface InfiniteScrollRequestOptions {
  instrument: string;
  timeframe: string;
  direction: 'left' | 'right';
  count: number;
  sampling: 'none' | 'uniform' | 'adaptive' | 'time_based';
  /** Load data before this timestamp (for historical/left direction) */
  before_timestamp?: number;
  /** Load data after this timestamp (for recent/right direction) */
  after_timestamp?: number;
}

// API response for infinite scrolling
export interface InfiniteScrollApiResponse {
  ohlc: OHLCData[];
  volume: VolumeData[];
  hasMore: boolean;
  nextTimestamp?: number;
  loadedRange: {
    start: number;
    end: number;
    barCount: number;
  };
}

// Performance metrics
export interface InfiniteScrollMetrics {
  totalLoadRequests: number;
  successfulLoads: number;
  failedLoads: number;
  averageLoadTimeMs: number;
  totalBarsLoaded: number;
  totalMemoryTrimmed: number;
  currentMemoryUsageMB: number;
  cacheHitRate: number;
}