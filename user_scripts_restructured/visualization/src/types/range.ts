// Range type system for trading chart time period selection

type Time = string | number;

/**
 * Standard trading time ranges
 */
export type Range = 
  | '1D'    // 1 Day
  | '1W'    // 1 Week  
  | '1M'    // 1 Month
  | '3M'    // 3 Months
  | '6M'    // 6 Months
  | '1Y'    // 1 Year
  | 'YTD'   // Year to Date
  | 'ALL'   // All available data
  | 'CUSTOM'; // Custom date range

/**
 * Range configuration defining how each range is calculated
 */
export interface RangeConfig {
  range: Range;
  days?: number;
  label: string;
  isRelative: boolean;
  calculateFromCurrent: boolean;
  description?: string;
  shortLabel?: string;
}

/**
 * Custom range specification with specific dates
 */
export interface CustomRange {
  startDate: Date;
  endDate: Date;
  label: string;
  description?: string;
}

/**
 * Result of range calculation with timestamps and metadata
 */
export interface RangeCalculationResult {
  startTimestamp: number;
  endTimestamp: number;
  estimatedBars: number;
  cacheKey: string;
  range: Range;
  timeframe: string;
  isCustom: boolean;
  metadata?: Record<string, unknown>;
}

/**
 * Range validation result
 */
export interface RangeValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  adjustedRange?: RangeCalculationResult;
}

/**
 * Range selection state
 */
export interface RangeState {
  currentRange: Range;
  customRange?: CustomRange;
  lastCalculation?: RangeCalculationResult;
  isLoading: boolean;
  error?: string;
}

/**
 * Range switching options
 */
export interface RangeSwitchOptions {
  preserveData?: boolean;
  showLoadingIndicator?: boolean;
  validateData?: boolean;
  preloadAdjacent?: boolean;
  updateUrl?: boolean;
}

/**
 * Trading hours specification for range calculations
 */
export interface TradingHours {
  open: string;  // HH:MM format
  close: string; // HH:MM format
  timezone?: string;
}

/**
 * Range calculation options
 */
export interface RangeCalculationOptions {
  timezone?: string;
  tradingHours?: TradingHours;
  includeExtendedHours?: boolean;
  adjustForHolidays?: boolean;
  maxDataPoints?: number;
}

/**
 * Range event types
 */
export interface RangeEvents {
  'range:changed': (range: Range, calculation: RangeCalculationResult) => void;
  'range:loading': (range: Range) => void;
  'range:loaded': (range: Range, calculation: RangeCalculationResult) => void;
  'range:error': (range: Range, error: string) => void;
  'range:validated': (result: RangeValidationResult) => void;
}

/**
 * Predefined range configurations
 */
export const RANGE_PRESETS: Record<Range, RangeConfig> = {
  '1D': {
    range: '1D',
    days: 1,
    label: '1 Day',
    shortLabel: '1D',
    description: 'Last 24 hours of trading data',
    isRelative: true,
    calculateFromCurrent: true,
  },
  '1W': {
    range: '1W',
    days: 7,
    label: '1 Week',
    shortLabel: '1W',
    description: 'Last 7 days of trading data',
    isRelative: true,
    calculateFromCurrent: true,
  },
  '1M': {
    range: '1M',
    days: 30,
    label: '1 Month',
    shortLabel: '1M',
    description: 'Last 30 days of trading data',
    isRelative: true,
    calculateFromCurrent: true,
  },
  '3M': {
    range: '3M',
    days: 90,
    label: '3 Months',
    shortLabel: '3M',
    description: 'Last 90 days of trading data',
    isRelative: true,
    calculateFromCurrent: true,
  },
  '6M': {
    range: '6M',
    days: 180,
    label: '6 Months',
    shortLabel: '6M',
    description: 'Last 180 days of trading data',
    isRelative: true,
    calculateFromCurrent: true,
  },
  '1Y': {
    range: '1Y',
    days: 365,
    label: '1 Year',
    shortLabel: '1Y',
    description: 'Last 365 days of trading data',
    isRelative: true,
    calculateFromCurrent: true,
  },
  'YTD': {
    range: 'YTD',
    label: 'Year to Date',
    shortLabel: 'YTD',
    description: 'From January 1st to current date',
    isRelative: false,
    calculateFromCurrent: false,
  },
  'ALL': {
    range: 'ALL',
    label: 'All Data',
    shortLabel: 'ALL',
    description: 'All available historical data',
    isRelative: false,
    calculateFromCurrent: false,
  },
  'CUSTOM': {
    range: 'CUSTOM',
    label: 'Custom Range',
    shortLabel: 'Custom',
    description: 'User-specified date range',
    isRelative: false,
    calculateFromCurrent: false,
  },
};

/**
 * Default range for new chart instances
 */
export const DEFAULT_RANGE: Range = '1D';

/**
 * Range display order for UI components
 */
export const RANGE_DISPLAY_ORDER: Range[] = [
  '1D', '1W', '1M', '3M', '6M', '1Y', 'YTD', 'ALL'
];

/**
 * Range calculation utilities
 */
export class RangeUtils {
  /**
   * Check if range is relative (calculated from current time)
   */
  static isRelativeRange(range: Range): boolean {
    return RANGE_PRESETS[range].isRelative;
  }

  /**
   * Check if range is custom
   */
  static isCustomRange(range: Range): boolean {
    return range === 'CUSTOM';
  }

  /**
   * Get range configuration
   */
  static getRangeConfig(range: Range): RangeConfig {
    return RANGE_PRESETS[range];
  }

  /**
   * Get range label
   */
  static getRangeLabel(range: Range, short = false): string {
    const config = RANGE_PRESETS[range];
    return short ? (config.shortLabel || config.label) : config.label;
  }

  /**
   * Get range description
   */
  static getRangeDescription(range: Range): string {
    return RANGE_PRESETS[range].description || RANGE_PRESETS[range].label;
  }

  /**
   * Calculate days for range
   */
  static getDaysForRange(range: Range): number | undefined {
    return RANGE_PRESETS[range].days;
  }

  /**
   * Validate range value
   */
  static isValidRange(value: unknown): value is Range {
    return typeof value === 'string' && value in RANGE_PRESETS;
  }

  /**
   * Get next range in sequence
   */
  static getNextRange(currentRange: Range): Range {
    const currentIndex = RANGE_DISPLAY_ORDER.indexOf(currentRange);
    const nextIndex = (currentIndex + 1) % RANGE_DISPLAY_ORDER.length;
    return RANGE_DISPLAY_ORDER[nextIndex];
  }

  /**
   * Get previous range in sequence
   */
  static getPreviousRange(currentRange: Range): Range {
    const currentIndex = RANGE_DISPLAY_ORDER.indexOf(currentRange);
    const prevIndex = currentIndex === 0 ? RANGE_DISPLAY_ORDER.length - 1 : currentIndex - 1;
    return RANGE_DISPLAY_ORDER[prevIndex];
  }

  /**
   * Compare two ranges by their time span
   */
  static compareRanges(rangeA: Range, rangeB: Range): number {
    const orderA = RANGE_DISPLAY_ORDER.indexOf(rangeA);
    const orderB = RANGE_DISPLAY_ORDER.indexOf(rangeB);
    return orderA - orderB;
  }

  /**
   * Get appropriate range for timeframe
   */
  static getDefaultRangeForTimeframe(timeframe: string): Range {
    // Suggest appropriate ranges based on timeframe
    switch (timeframe) {
      case '1min':
      case '5min':
        return '1D';
      case '15min':
      case '30min':
        return '1W';
      case '1hour':
      case '4hour':
        return '1M';
      case '1day':
        return '1Y';
      default:
        return DEFAULT_RANGE;
    }
  }

  /**
   * Format range for URL parameters
   */
  static formatRangeForUrl(range: Range, customRange?: CustomRange): string {
    if (range === 'CUSTOM' && customRange) {
      const start = customRange.startDate.toISOString().split('T')[0];
      const end = customRange.endDate.toISOString().split('T')[0];
      return `custom_${start}_${end}`;
    }
    return range.toLowerCase();
  }

  /**
   * Parse range from URL parameters
   */
  static parseRangeFromUrl(urlRange: string): { range: Range; customRange?: CustomRange } {
    if (urlRange.startsWith('custom_')) {
      const parts = urlRange.split('_');
      if (parts.length === 3) {
        try {
          const startDate = new Date(parts[1]);
          const endDate = new Date(parts[2]);
          return {
            range: 'CUSTOM',
            customRange: {
              startDate,
              endDate,
              label: `${parts[1]} to ${parts[2]}`,
            },
          };
        } catch {
          return { range: DEFAULT_RANGE };
        }
      }
    }

    const range = urlRange.toUpperCase() as Range;
    return this.isValidRange(range) ? { range } : { range: DEFAULT_RANGE };
  }
}

/**
 * Type guard for range validation
 */
export function isRange(value: unknown): value is Range {
  return RangeUtils.isValidRange(value);
}

/**
 * Type guard for custom range validation
 */
export function isCustomRange(value: unknown): value is CustomRange {
  if (!value || typeof value !== 'object') return false;
  
  const customRange = value as CustomRange;
  return (
    customRange.startDate instanceof Date &&
    customRange.endDate instanceof Date &&
    typeof customRange.label === 'string' &&
    customRange.startDate < customRange.endDate
  );
}

/**
 * Type guard for range calculation result
 */
export function isRangeCalculationResult(value: unknown): value is RangeCalculationResult {
  if (!value || typeof value !== 'object') return false;
  
  const result = value as RangeCalculationResult;
  return (
    typeof result.startTimestamp === 'number' &&
    typeof result.endTimestamp === 'number' &&
    typeof result.estimatedBars === 'number' &&
    typeof result.cacheKey === 'string' &&
    typeof result.timeframe === 'string' &&
    typeof result.isCustom === 'boolean' &&
    isRange(result.range) &&
    result.startTimestamp < result.endTimestamp
  );
}

// Export everything
export default {
  RANGE_PRESETS,
  DEFAULT_RANGE,
  RANGE_DISPLAY_ORDER,
  RangeUtils,
  isRange,
  isCustomRange,
  isRangeCalculationResult,
};