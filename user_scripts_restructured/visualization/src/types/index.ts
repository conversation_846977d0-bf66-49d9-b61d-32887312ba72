// Re-export all types for easy importing
export * from './chart';
export * from './websocket';
export * from './api';
export * from './infinite-scroll';
export * from './chart-sync';
export * from './globals';
export * from './range';
export * from './data-quality'; // Official data quality schema (export first to avoid conflicts)

// Re-export validation types with explicit naming to avoid conflicts
export type {
  DataStatistics,
  isValidTimeRange,
  isValidLogicalRange,
  isValidOHLCData,
  isValidVolumeData,
  TimeRange
} from './validation';

// Explicitly re-export chart options to avoid conflicts
export type {
  ChartOptions as CustomChartOptions,
  VolumeChartOptions,
  CandlestickSeriesOptions as CustomCandlestickSeriesOptions,
  VolumeSeriesOptions as CustomVolumeSeriesOptions
} from './chart-options';

// Import proper lightweight-charts types


// Global DOM types
export interface ChartElements {
  loadingEl: HTMLElement;
  errorEl: HTMLElement;
  chartInfoEl: HTMLElement;
  chartContainer: HTMLElement;
  volumeContainer: HTMLElement;
  barsCountEl: HTMLElement;
  dateRangeEl: HTMLElement;
  dataQualityEl: HTMLElement;
  connectionStatusEl: HTMLElement;
  infiniteScrollLoadingEl?: HTMLElement;
  infiniteScrollStatusEl?: HTMLElement;
  // Range-related elements
  currentRangeEl?: HTMLElement;
  estimatedBarsEl?: HTMLElement;
}

export interface TimeframeElements {
  buttons: NodeListOf<HTMLButtonElement>;
  selector: HTMLElement;
}

// Utility types
export type Nullable<T> = T | null;
export type Optional<T> = T | undefined;

// Enhanced Chart instance types with proper lightweight-charts integration
export type ChartInstance = IChartApi;
export type SeriesInstance = ISeriesApi<'Candlestick'>;
export type VolumeSeriesInstance = ISeriesApi<'Histogram'>;
export type TimeScaleInstance = ITimeScaleApi<Time>;

// Event handler types with proper typing
export type ChartEventHandler<T = unknown> = (data: T) => void;
export type TimeframeChangeHandler = (timeframe: string) => void;
export type ErrorHandler = (error: Error | string) => void;
export type LogicalRangeChangeHandler = (range: LogicalRange | null) => void;
export type TimeRangeChangeHandler = (range: { from: Time; to: Time } | null) => void;