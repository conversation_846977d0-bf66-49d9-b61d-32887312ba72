// Data validation types for enhanced type safety
// Re-export from official data quality schema for compatibility

import type { 
  DataQualityMetrics as OfficialDataQualityMetrics,
  ValidationResult as OfficialValidationResult,
  ValidationIssue as OfficialValidationIssue,
  ValidationContext as OfficialValidationContext
} from './data-quality';

// Use official data quality metrics with compatibility aliases
export type DataQualityMetrics = OfficialDataQualityMetrics & {
  // Legacy compatibility fields
  missing_bars?: number;
  invalid_values?: number;
};

export interface DataStatistics {
  timeframe: string;
  start_time: string;
  end_time: string;
  total_bars: number;
  price_range: {
    high: number;
    low: number;
  };
  volume_stats: {
    total: number;
    average: number;
    max: number;
  };
  // Compatibility with existing ChartStatistics
  date_range?: {
    start: string;
    end: string;
  };
  bars_count?: number;
}

// Use official validation interfaces with compatibility adaptations
export type LegacyValidationResult = OfficialValidationResult & {
  // Legacy compatibility field (maps to is_valid)
  isValid: boolean;
};

export type LegacyValidationError = OfficialValidationIssue & {
  // All fields already compatible
};

export type LegacyValidationWarning = OfficialValidationIssue & {
  severity: 'warning';
};

// Re-export official types for convenience
export type { 
  ValidationResult,
  ValidationIssue as ValidationError,
  ValidationIssue as ValidationWarning
} from './data-quality';

// Type guards for runtime validation
export function isValidTimeRange(value: unknown): value is TimeRange {
  if (!value || typeof value !== 'object') return false;
  
  const timeRange = value as TimeRange;
  return (
    typeof timeRange.from === 'number' &&
    typeof timeRange.to === 'number' &&
    timeRange.from < timeRange.to
  );
}

export function isValidLogicalRange(value: unknown): value is LogicalRange {
  if (!value || typeof value !== 'object') return false;
  
  const logicalRange = value as LogicalRange;
  return (
    typeof logicalRange.from === 'number' &&
    typeof logicalRange.to === 'number' &&
    logicalRange.from >= 0 &&
    logicalRange.to >= 0 &&
    logicalRange.from <= logicalRange.to
  );
}

export function isValidOHLCData(value: unknown): value is OHLCData {
  if (!value || typeof value !== 'object') return false;
  
  const ohlc = value as OHLCData;
  return (
    typeof ohlc.time === 'number' &&
    typeof ohlc.open === 'number' &&
    typeof ohlc.high === 'number' &&
    typeof ohlc.low === 'number' &&
    typeof ohlc.close === 'number' &&
    ohlc.high >= Math.max(ohlc.open, ohlc.close) &&
    ohlc.low <= Math.min(ohlc.open, ohlc.close)
  );
}

export function isValidVolumeData(value: unknown): value is VolumeData {
  if (!value || typeof value !== 'object') return false;
  
  const volume = value as VolumeData;
  return (
    typeof volume.time === 'number' &&
    typeof volume.value === 'number' &&
    volume.value >= 0
  );
}

// Re-export from existing types to maintain compatibility
import type { OHLCData, VolumeData } from './chart';


export type { OHLCData, VolumeData, LogicalRange };

// Define TimeRange for our use case
export interface TimeRange {
  from: Time;
  to: Time;
}

export type { Time };