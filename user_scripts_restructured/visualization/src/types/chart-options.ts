// Chart configuration options with proper typing



// Extended chart options with our custom settings
export interface ChartOptions extends DeepPartial<LightweightChartOptions> {
  // Custom options
  autoSize?: boolean;
  theme?: 'light' | 'dark';
}

export interface VolumeChartOptions extends ChartOptions {
  height?: number; // Volume chart typically shorter
}

export interface CandlestickSeriesOptions extends DeepPartial<LightweightCandlestickOptions> {
  // Standard candlestick options
  upColor?: string;
  downColor?: string;
  borderVisible?: boolean;
  wickVisible?: boolean;
  borderColor?: string;
  wickColor?: string;
  borderUpColor?: string;
  borderDownColor?: string;
  wickUpColor?: string;
  wickDownColor?: string;
}

export interface VolumeSeriesOptions extends DeepPartial<LightweightHistogramOptions> {
  // Volume-specific options
  color?: string;
  priceFormat?: {
    type: 'volume';
    precision?: number;
    minMove?: number;
  };
  priceScaleId?: string;
}

// Use default options directly from chart implementations
// These are defined in the chart managers where they're used