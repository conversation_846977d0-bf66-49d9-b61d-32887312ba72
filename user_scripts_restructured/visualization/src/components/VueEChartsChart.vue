<template>
  <div class="vue-echarts-chart">
    <!-- Vue-ECharts component with fixed manual update pattern -->
    <v-chart 
      ref="chartRef"
      class="chart"
      :option="chartOption" 
      :loading="isLoading"
      :autoresize="true"
      :theme="'dark'"
      :manual-update="false"
      :init-options="{
        renderer: 'canvas',
        devicePixelRatio: 1,
        width: 'auto',
        height: 'auto'
      }"
      @dataZoom="onDataZoom"
      @click="onClick"
      @mouseover="onMouseOver"
      @mouseout="onMouseOut"
    />
    
    <!-- Performance monitoring overlay -->
    <div v-if="isMonitoring && healthScore < 75" class="performance-overlay">
      <div class="performance-info">
        <span class="health-score" :class="healthStatus">{{ healthScore }}/100</span>
        <div class="network-deduplication-info">
          <div class="metric">Dedup: {{ Math.round((getDeduplicationMetrics().deduplicatedRequests / Math.max(getDeduplicationMetrics().totalRequests, 1)) * 100) }}%</div>
          <div class="metric">Cache: {{ getDeduplicationCacheStatus().size }}/{{ getDeduplicationCacheStatus().maxSize }}</div>
        </div>
        <div v-if="recommendations.length > 0" class="recommendations">
          <div v-for="rec in recommendations.slice(0, 2)" :key="rec" class="recommendation">
            {{ rec }}
          </div>
        </div>
      </div>
    </div>

    <!-- Error overlay (simplified) -->
    <div v-if="error" class="error-overlay">
      <div class="error-message">
        <h3>Chart Error</h3>
        <p>{{ error }}</p>
        <button @click="retry" class="retry-btn">Retry</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onBeforeUnmount, onErrorCaptured, nextTick, markRaw } from 'vue';
import VChart from 'vue-echarts';
import type { EChartsOption } from 'echarts';
import { useEChartsAPI } from '../composables/useEChartsAPI';
import { useEnhancedPerformanceMonitor } from '../composables/useEnhancedPerformanceMonitor';
import { useBoundedLoadQueue } from '../utils/bounded-load-queue';
import { DataValidator, ChartDataError, type ChartData } from '../utils/data-validator';
import { useNetworkDeduplication } from '../utils/network-request-deduplication';
import type { InfiniteScrollRequestOptions } from '../types/infinite-scroll';

// ECharts components are registered globally in vue-echarts-app.ts
// No need to register again here to avoid conflicts

// Props
interface Props {
  instrument: string;
  timeframe: string;
  chartConfig?: any;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  dataLoaded: [data: any];
  error: [error: any];
  scrollToEdge: [direction: 'left' | 'right'];
}>();

// State
const chartRef = ref();
const isLoading = ref(false);
const error = ref<string | null>(null);
const chartData = ref<any[]>([]);
const retryCount = ref(0);
const maxRetries = ref(3);

// Phase 2.5: ECharts large mode optimization for 720K+ datasets
const echartsLargeMode = ref(false);

// Simplified performance mode without manual updates
const performanceCriticalMode = ref(false);
const manualUpdateThreshold = 5000; // Performance critical threshold

// Composables
const { loadChartData, loadInfiniteScrollData } = useEChartsAPI();
const {
  trackLoadTime,
  trackRenderTime,
  trackNetworkRequest,
  trackError,
  healthScore,
  healthStatus,
  recommendations,
  isMonitoring
} = useEnhancedPerformanceMonitor();

// Initialize network request deduplication for preventing duplicate API calls
const {
  request: dedupedRequest,
  getMetrics: getDeduplicationMetrics,
  getCacheStatus: getDeduplicationCacheStatus,
  abortAll: abortAllRequests
} = useNetworkDeduplication({
  maxCacheSize: 50,
  requestTimeout: 30000,
  maxRetries: 3,
  retryDelay: 1000,
  cacheTTL: 5 * 60 * 1000, // 5 minutes
  enableMetrics: true,
  debugMode: false
});

// Initialize bounded load queue for WebSocket message overflow prevention
const { queue: loadQueue, getMetrics: getQueueMetrics, getStatus: getQueueStatus } = useBoundedLoadQueue(
  async (request) => {
    // Process queued load requests with full race condition protection
    console.log(`🔄 Processing queued ${request.direction} load for ${request.instrument}`);
    
    // Create abort controller for this queued operation
    const abortController = new AbortController();
    
    try {
      await loadMoreDataWithAbort(request.direction, abortController.signal);
    } catch (error: any) {
      if (error.name !== 'AbortError') {
        console.error(`Queued load failed (${request.direction}):`, error);
        throw error;
      }
    }
  },
  {
    maxSize: 25, // Smaller queue for UI responsiveness
    maxAge: 20000, // 20 seconds for UI operations
    processingInterval: 150, // Slightly slower for UI smoothness
    enableMetrics: true
  }
);

// Static chart configuration (separated from reactive data)
const staticChartOption = {
  // Comprehensive animation disabling for performance
  animation: false,
  animationDuration: 0,
  progressive: 0,
  progressiveThreshold: 0,
  
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross' as const
    },
    formatter: (params: any) => {
      if (!params || params.length === 0) return '';
      
      const param = params[0];
      if (!param || !param.data) return '';
      
      const [open, close, low, high] = param.data;
      const categoryData = chartData.value.map(item => item.time || '');
      const time = categoryData[param.dataIndex];
      
      return `
        <div style="text-align: left;">
          <strong>${time}</strong><br/>
          Open: ${open.toFixed(2)}<br/>
          High: ${high.toFixed(2)}<br/>
          Low: ${low.toFixed(2)}<br/>
          Close: ${close.toFixed(2)}
        </div>
      `;
    }
  },
  
  xAxis: {
    type: 'category' as const,
    boundaryGap: false,
    axisLine: { lineStyle: { color: '#404040' } },
    axisLabel: { color: '#cccccc' },
    animation: false
  },
  
  yAxis: {
    type: 'value' as const,
    scale: true,
    axisLine: { lineStyle: { color: '#404040' } },
    axisLabel: { color: '#cccccc' },
    splitLine: { lineStyle: { color: '#333333' } },
    animation: false
  },
  
  dataZoom: [
    {
      type: 'inside',
      start: 80,
      end: 100,
      throttle: 100
    },
    {
      type: 'slider',
      start: 80,
      end: 100,
      bottom: '5%',
      height: '8%',
      throttle: 100
    }
  ]
};

// Enhanced chart initialization guards
const isChartDataValid = computed(() => {
  if (!chartData.value || chartData.value.length === 0) {
    console.warn('⚠️ Chart data validation failed: No data available');
    return false;
  }
  
  // Validate data structure
  const firstItem = chartData.value[0];
  const hasRequiredFields = firstItem && 
    typeof firstItem.open === 'number' && 
    typeof firstItem.high === 'number' && 
    typeof firstItem.low === 'number' && 
    typeof firstItem.close === 'number' && 
    firstItem.time;
    
  if (!hasRequiredFields) {
    console.warn('⚠️ Chart data validation failed: Invalid data structure', firstItem);
    return false;
  }
  
  console.log('✅ Chart data validation passed:', {
    length: chartData.value.length,
    firstItem,
    lastItem: chartData.value[chartData.value.length - 1]
  });
  
  return true;
});

// Optimized chart option using static configuration with enhanced guards
const chartOption = computed((): EChartsOption => {
  console.log(`🔄 chartOption computed triggered - chartData length: ${chartData.value?.length || 0}, isValid: ${isChartDataValid.value}`);
  
  // Enhanced initialization guards
  if (!isChartDataValid.value) {
    const message = !chartData.value || chartData.value.length === 0 
      ? 'Loading chart data...' 
      : 'Invalid chart data format';
    
    console.log(`📊 Chart showing fallback state: ${message}`);
    
    return {
      title: {
        text: message,
        left: 'center',
        top: 'center',
        textStyle: {
          color: '#cccccc',
          fontSize: 16
        }
      },
      backgroundColor: '#1a1a1a' // Ensure background color is set
    };
  }

  console.log(`📊 Computing chart option with ${chartData.value.length} data points`);
  
  // Enhanced data validation guards
  try {
    // Validate data structure before processing
    if (!Array.isArray(chartData.value)) {
      console.error('Chart data is not an array:', typeof chartData.value);
      throw new Error('Invalid data structure: not an array');
    }
    
    if (chartData.value.length === 0) {
      console.warn('Chart data array is empty');
      throw new Error('No data available');
    }
    
    // Sample validation - check first and last items
    const firstItem = chartData.value[0];
    const lastItem = chartData.value[chartData.value.length - 1];
    
    for (const [index, item] of [[0, firstItem], [chartData.value.length - 1, lastItem]]) {
      if (!item || typeof item !== 'object') {
        console.error(`Invalid data item at index ${index}:`, item);
        throw new Error(`Invalid data item at index ${index}`);
      }
      
      // Check required numeric fields
      const requiredFields = ['open', 'high', 'low', 'close'];
      for (const field of requiredFields) {
        const value = parseFloat(item[field]);
        if (isNaN(value) || !isFinite(value)) {
          console.error(`Invalid ${field} value at index ${index}:`, item[field]);
          throw new Error(`Invalid ${field} value at index ${index}`);
        }
      }
    }
    
    console.log(`📊 Data validation passed for ${chartData.value.length} items`);
    
    // Transform data for ECharts candlestick format with validation
    const candlestickData = chartData.value.map((item, index) => {
      const open = parseFloat(item.open);
      const close = parseFloat(item.close);
      const low = parseFloat(item.low);
      const high = parseFloat(item.high);
      
      // Validate OHLC relationship
      if (low > high || open < low || open > high || close < low || close > high) {
        console.warn(`Invalid OHLC relationship at index ${index}:`, {open, high, low, close});
        // Correct the values
        const correctedLow = Math.min(open, close, low, high);
        const correctedHigh = Math.max(open, close, low, high);
        return [open, close, correctedLow, correctedHigh];
      }
      
      return [open, close, low, high];
    });

    const categoryData = chartData.value.map((item, index) => {
      if (!item.timeString && !item.time) {
        console.warn(`Missing time data at index ${index}`);
        return `Item ${index}`;
      }
      return item.timeString || new Date(item.time).toLocaleTimeString('en-US', { 
        hour: '2-digit', 
        minute: '2-digit',
        hour12: false 
      });
    });

    // Merge static configuration with dynamic data
    const finalOption = {
      ...staticChartOption,
      backgroundColor: '#1a1a1a', // Ensure consistent background
      
      title: {
        text: `${props.instrument} - ${props.timeframe}`,
        left: 'center',
        textStyle: {
          color: '#ffffff'
        }
      },
      
      xAxis: {
        ...staticChartOption.xAxis,
        data: categoryData
      },
      
      series: [
        {
          name: props.instrument,
          type: 'candlestick',
          data: candlestickData,
          itemStyle: {
            color: '#00d4aa',
            color0: '#ff4444',
            borderColor: '#00d4aa',
            borderColor0: '#ff4444'
          },
          animation: false,
          // Phase 2.5: Enhanced ECharts large mode optimization for 720K+ datasets
          large: echartsLargeMode.value,
          largeThreshold: echartsLargeMode.value ? 1000 : 2000, // Lower threshold for large datasets
          // Progressive rendering for massive datasets - critical for memory stability
          progressive: echartsLargeMode.value ? 200 : 0, // Smaller chunks for better memory performance
          progressiveThreshold: echartsLargeMode.value ? 1500 : 0, // Enable for smaller datasets in large mode
          progressiveChunkMode: 'mod', // Better visual effect during rendering
          // Additional large dataset optimizations
          sampling: echartsLargeMode.value ? 'lttb' : 'none', // LTTB sampling for large datasets
          // Disable hover animations for performance
          emphasis: {
            disabled: echartsLargeMode.value
          },
          // Optimize rendering for large datasets
          blendMode: echartsLargeMode.value ? 'lighter' : 'source-over'
        }
      ]
    };

    console.log(`📊 Final chart option created successfully`);
    return finalOption;
    
  } catch (validationError: any) {
    console.error('📊 Chart option computation failed:', validationError);
    
    // Return error state chart option
    return {
      title: {
        text: `Chart Error: ${validationError.message || 'Data validation failed'}`,
        left: 'center',
        top: 'center',
        textStyle: {
          color: '#ff6b6b',
          fontSize: 14
        }
      },
      backgroundColor: '#1a1a1a',
      subtitle: {
        text: 'Check console for details',
        left: 'center',
        top: '60%',
        textStyle: {
          color: '#cccccc',
          fontSize: 12
        }
      }
    };
  }
});

// Optimized chart update method using reactive mode only
const optimizedChartUpdate = () => {
  if (!chartRef.value) return;
  
  try {
    // Vue-ECharts handles updates automatically through reactive chartOption
    console.log(`⚡ Chart update completed: ${chartData.value.length} data points (Vue reactive mode)`);
  } catch (error) {
    console.error('Error in chart update:', error);
  }
};

// Simplified chart update using Vue reactive system
const updateChartDataReactive = () => {
  // Vue-ECharts automatically updates when chartData.value changes
  // No manual intervention needed - the computed chartOption handles it
  console.log(`Reactive chart update: ${chartData.value.length} data points`);
};

// Enhanced infinite scroll edge detection with race condition prevention
const scrollState = ref({
  isLoading: false,
  leftEdgeReached: false,
  rightEdgeReached: false,
  lastScrollTime: 0,
  scrollDebounce: 250, // ms
  minLoadThreshold: 2, // Minimum percentage from edge
  maxLoadThreshold: 8 // Maximum percentage from edge
});

// Advanced concurrency control following bug analysis recommendations
class ScrollLoadManager {
  private loadingPromise: Promise<void> | null = null;
  private abortController: AbortController | null = null;
  private abortTimeout: NodeJS.Timeout | null = null; // Fix: Add timeout tracking for cleanup
  private readonly requestTimeout = 15000; // 15 seconds
  
  async handleScrollLoad(direction: 'left' | 'right', priority: 'high' | 'normal' | 'low' = 'normal'): Promise<void> {
    try {
      // Check if we can process immediately or need to queue
      if (this.loadingPromise) {
        // Queue the request using the bounded load queue
        const queued = loadQueue.enqueue({
          instrument: props.instrument,
          timeframe: props.timeframe,
          direction,
          count: 50,
          priority
        });
        
        if (queued) {
          console.log(`📥 Queued ${direction} scroll load (priority: ${priority})`);
          
          // Log queue status for debugging
          const queueStatus = getQueueStatus();
          console.log(`📊 Queue status: ${queueStatus.size}/${queueStatus.maxSize} (${queueStatus.utilizationPercentage.toFixed(1)}%)`);
        } else {
          console.warn(`⚠️ Failed to queue ${direction} scroll load - queue full`);
        }
        
        return;
      }
      
      // Cancel any pending operation
      if (this.abortController) {
        console.log(`🚫 Cancelling previous ${direction} scroll operation`);
        this.abortController.abort();
        this.abortController = null; // Fix: Ensure immediate cleanup
      }
      
      // Create new abort controller for this operation
      this.abortController = new AbortController();
      const signal = this.abortController.signal;
      
      // Fix: Add timeout mechanism to prevent orphaned controllers
      this.abortTimeout = setTimeout(() => {
        if (this.abortController) {
          console.warn(`⏰ Scroll load timeout after ${this.requestTimeout}ms, aborting ${direction} operation`);
          this.abortController.abort();
          this.abortController = null;
          this.abortTimeout = null;
        }
      }, this.requestTimeout);
      
      // Start new operation immediately
      this.loadingPromise = this.executeScrollLoad(direction, signal);
      
      try {
        await this.loadingPromise;
      } finally {
        // Fix: Enhanced cleanup - ensure all resources are freed
        this.loadingPromise = null;
        if (this.abortTimeout) {
          clearTimeout(this.abortTimeout);
          this.abortTimeout = null;
        }
        if (this.abortController) {
          this.abortController.abort();
          this.abortController = null;
        }
      }
      
    } catch (error: any) {
      // Fix: Ensure cleanup happens even in outer catch block
      if (this.abortTimeout) {
        clearTimeout(this.abortTimeout);
        this.abortTimeout = null;
      }
      if (this.abortController) {
        this.abortController.abort();
        this.abortController = null;
      }
      this.loadingPromise = null;
      
      if (error.name !== 'AbortError') {
        console.error(`Scroll load error (${direction}):`, error);
        handleError(error, `Infinite Scroll (${direction})`);
      }
    }
  }
  
  private async executeScrollLoad(direction: 'left' | 'right', signal: AbortSignal): Promise<void> {
    // Check abort before network operation
    if (signal.aborted) {
      throw new DOMException('Aborted', 'AbortError');
    }
    
    console.log(`🔄 Executing ${direction} scroll load with race protection`);
    
    // Set loading state
    scrollState.value.isLoading = true;
    
    try {
      // Execute the actual load with abort signal checking
      await loadMoreDataWithAbort(direction, signal);
      
      // Check abort after load completes
      if (signal.aborted) {
        throw new DOMException('Aborted', 'AbortError');
      }
      
      console.log(`✅ ${direction} scroll load completed successfully`);
      
    } finally {
      // Always reset loading state
      scrollState.value.isLoading = false;
    }
  }
  
  cancelAll(): void {
    // Fix: Ensure timeout is also cleared in cancelAll
    if (this.abortTimeout) {
      clearTimeout(this.abortTimeout);
      this.abortTimeout = null;
    }
    if (this.abortController) {
      this.abortController.abort();
      this.abortController = null;
    }
    
    this.loadingPromise = null;
    scrollState.value.isLoading = false;
    
    // Clear the bounded load queue
    loadQueue.clear();
    
    console.log('🧹 All scroll operations cancelled');
  }
}

// Initialize the scroll load manager
const scrollLoadManager = new ScrollLoadManager();

// Event handlers for infinite scroll with enhanced edge case handling
const onDataZoom = (params: any) => {
  console.log('DataZoom event:', params);
  
  const now = Date.now();
  
  // Debounce rapid scroll events
  if (now - scrollState.value.lastScrollTime < scrollState.value.scrollDebounce) {
    return;
  }
  
  scrollState.value.lastScrollTime = now;
  
  // Don't trigger if already loading
  if (scrollState.value.isLoading) {
    console.log('Scroll ignored: already loading data');
    return;
  }
  
  // Enhanced threshold detection with adaptive logic
  const adaptiveThreshold = Math.max(
    scrollState.value.minLoadThreshold,
    Math.min(scrollState.value.maxLoadThreshold, 100 / chartData.value.length * 10)
  );
  
  // Left edge detection (historical data)
  if (params.start <= adaptiveThreshold && !scrollState.value.leftEdgeReached) {
    console.log(`Triggered infinite scroll: left edge (threshold: ${adaptiveThreshold}%)`);
    
    // Prevent duplicate left edge loads
    scrollState.value.leftEdgeReached = true;
    setTimeout(() => {
      scrollState.value.leftEdgeReached = false;
    }, 1000);
    
    emit('scrollToEdge', 'left');
    loadMoreDataWithState('left');
  } 
  // Right edge detection (recent data) 
  else if (params.end >= (100 - adaptiveThreshold) && !scrollState.value.rightEdgeReached) {
    console.log(`Triggered infinite scroll: right edge (threshold: ${adaptiveThreshold}%)`);
    
    // Prevent duplicate right edge loads
    scrollState.value.rightEdgeReached = true;
    setTimeout(() => {
      scrollState.value.rightEdgeReached = false;
    }, 1000);
    
    emit('scrollToEdge', 'right');
    loadMoreDataWithState('right');
  }
};

const onClick = (params: any) => {
  console.log('Chart clicked:', params);
};

const onMouseOver = (params: any) => {
  // Handle mouse over events if needed
};

const onMouseOut = (params: any) => {
  // Handle mouse out events if needed
};

// Enhanced error handling
const handleError = (err: any, context: string = 'Unknown') => {
  console.error(`Vue-ECharts error in ${context}:`, err);
  
  const errorMessage = err?.message || err?.toString() || 'Unknown error occurred';
  error.value = `${context}: ${errorMessage}`;
  
  emit('error', {
    error: err,
    context,
    timestamp: new Date().toISOString(),
    retryCount: retryCount.value
  });
};

// Data loading methods with enhanced error handling and performance tracking
const loadData = async () => {
  const startTime = performance.now();
  
  try {
    isLoading.value = true;
    error.value = null;
    
    // Use network deduplication to prevent multiple identical API calls
    const data = await dedupedRequest(
      {
        url: `/api/chart-data/${props.instrument}`,
        method: 'GET',
        params: {
          timeframe: props.timeframe,
          limit: 100,
          sampling: 'none'
        }
      },
      async (abortSignal) => {
        return loadChartData(props.instrument, props.timeframe, {
          limit: 100,
          sampling: 'none'
        });
      }
    );
    
    const loadTime = performance.now() - startTime;
    trackLoadTime(loadTime);
    trackNetworkRequest('/api/chart-data', loadTime, true, JSON.stringify(data).length);
    
    // Transform and validate API data with comprehensive data integrity checks
    const renderStartTime = performance.now();
    
    try {
      // Validate the API response structure first
      const validatedResponse = DataValidator.validateAPIResponse(data);
      const rawBars = validatedResponse.ohlc || [];
      
      // Apply data sanitization and validation
      let validatedBars: ChartData;
      
      try {
        validatedBars = DataValidator.validateChartData(rawBars);
        console.log(`✅ Data validation successful: ${validatedBars.length} bars validated`);
      } catch (validationError) {
        if (validationError instanceof ChartDataError) {
          console.warn('⚠️ Data validation failed, attempting sanitization...', validationError.message);
          
          // Attempt to sanitize and fix common data issues
          validatedBars = DataValidator.sanitizeOHLCData(rawBars);
          console.log(`🔧 Data sanitized successfully: ${validatedBars.length} bars recovered from ${rawBars.length} raw bars`);
        } else {
          throw validationError;
        }
      }
      
      // Convert to non-reactive format to prevent Vue memory overload with large datasets
      const processedData = validatedBars.map(bar => ({
        time: bar.time * 1000, // Convert to milliseconds for JavaScript Date
        timeString: new Date(bar.time * 1000).toLocaleTimeString('en-US', { 
          hour: '2-digit', 
          minute: '2-digit',
          hour12: false 
        }),
        open: bar.open,
        high: bar.high,
        low: bar.low,
        close: bar.close,
        volume: bar.volume || 0
      }));
      
      // Use markRaw to prevent Vue reactivity on large data arrays (critical for 720K+ bars)
      // But ensure the assignment triggers the computed chartOption to re-evaluate
      const newData = markRaw(processedData);
      chartData.value = newData;
      
      // Force chart option recomputation for Vue-ECharts
      await nextTick();
      console.log(`🔄 Chart data updated: ${newData.length} bars, triggering chart option recomputation`);
      
      // Phase 2.5: Enhanced ECharts large mode activation for memory crisis
      const datasetSize = chartData.value.length;
      
      // Check memory pressure for aggressive large mode activation
      const memoryInfo = (performance as any).memory;
      const memoryUsageMB = memoryInfo ? memoryInfo.usedJSHeapSize / (1024 * 1024) : 0;
      const memoryLimitMB = memoryInfo ? memoryInfo.jsHeapSizeLimit / (1024 * 1024) : 0;
      const memoryPressure = memoryLimitMB ? (memoryUsageMB / memoryLimitMB) : 0;
      
      // Enable large mode for smaller datasets if memory pressure is high
      const shouldEnableLargeMode = datasetSize > 1000 || // Lower threshold 
                                   (datasetSize > 500 && memoryPressure > 0.7) || // Memory pressure
                                   (datasetSize > 2000); // Original threshold
      
      if (shouldEnableLargeMode) {
        echartsLargeMode.value = true;
        console.log(`📊 ECharts LARGE MODE ENABLED: ${datasetSize} data points, Memory: ${memoryUsageMB.toFixed(1)}MB (${(memoryPressure * 100).toFixed(1)}%)`);
      } else {
        echartsLargeMode.value = false;
        console.log(`📈 ECharts normal mode: ${datasetSize} data points, Memory: ${memoryUsageMB.toFixed(1)}MB`);
      }
      
      // Simplified performance monitoring for large datasets
      if (datasetSize > manualUpdateThreshold) {
        performanceCriticalMode.value = true;
        console.log(`⚡ Performance-critical mode ENABLED: ${datasetSize} data points > ${manualUpdateThreshold} threshold (Vue reactive)`);
      } else {
        performanceCriticalMode.value = false;
        console.log(`🔄 Vue reactive mode for ${datasetSize} data points`);
      }
      
    } catch (validationError) {
      console.error('💥 Critical data validation failure:', validationError);
      
      if (validationError instanceof ChartDataError) {
        handleError(validationError, 'Data Validation');
        return; // Don't proceed with invalid data
      } else {
        throw validationError;
      }
    }
    
    const renderTime = performance.now() - renderStartTime;
    trackRenderTime(renderTime);
    
    console.log('Vue-ECharts data loaded:', {
      length: chartData.value.length,
      sample: chartData.value[0],
      loadTime: `${loadTime.toFixed(2)}ms`,
      renderTime: `${renderTime.toFixed(2)}ms`,
      healthScore: healthScore.value
    });
    
    // Force chart refresh after data load
    await nextTick();
    if (chartRef.value && typeof chartRef.value.resize === 'function') {
      setTimeout(() => {
        console.log('🔄 Forcing chart refresh after data load');
        chartRef.value.resize();
      }, 50);
    }
    
    emit('dataLoaded', {
      ...data,
      processedBars: chartData.value,
      length: chartData.value.length,
      performance: {
        loadTime,
        renderTime,
        healthScore: healthScore.value,
        healthStatus: healthStatus.value
      }
    });
  } catch (err: any) {
    const loadTime = performance.now() - startTime;
    trackNetworkRequest('/api/chart-data', loadTime, false);
    trackError(err);
    handleError(err, 'Data Loading');
  } finally {
    isLoading.value = false;
  }
};

// Enhanced infinite scroll data loading with state management
// Enhanced load function with abort signal support
const loadMoreDataWithAbort = async (direction: 'left' | 'right', signal: AbortSignal) => {
  const startTime = performance.now();
  
  try {
    // Validate data integrity before loading
    if (!chartData.value || chartData.value.length === 0) {
      throw new Error('No chart data available for infinite scroll');
    }
    
    // Check abort before starting
    if (signal.aborted) {
      throw new DOMException('Aborted', 'AbortError');
    }
    
    const loadParams: InfiniteScrollRequestOptions = {
      instrument: props.instrument,
      timeframe: props.timeframe,
      direction,
      count: 50,
      sampling: 'adaptive'
    };
    
    // Enhanced timestamp handling with validation
    if (direction === 'left') {
      const firstItem = chartData.value[0];
      if (!firstItem?.time) {
        throw new Error('Invalid first item timestamp for historical loading');
      }
      loadParams.before_timestamp = new Date(firstItem.time).toISOString();
    } else {
      const lastItem = chartData.value[chartData.value.length - 1];
      if (!lastItem?.time) {
        throw new Error('Invalid last item timestamp for recent loading');
      }
      loadParams.after_timestamp = new Date(lastItem.time).toISOString();
    }
    
    // Check abort before network call
    if (signal.aborted) {
      throw new DOMException('Aborted', 'AbortError');
    }
    
    // Use network deduplication for infinite scroll requests to prevent duplicates
    const newData = await dedupedRequest(
      {
        url: `/api/infinite-scroll/${props.instrument}`,
        method: 'GET',
        params: {
          timeframe: loadParams.timeframe,
          direction: loadParams.direction,
          count: loadParams.count,
          sampling: loadParams.sampling,
          before_timestamp: loadParams.before_timestamp,
          after_timestamp: loadParams.after_timestamp
        }
      },
      async (abortSignal) => {
        // Check abort signal before proceeding
        if (abortSignal.aborted) {
          throw new DOMException('Aborted', 'AbortError');
        }
        return loadInfiniteScrollData(loadParams);
      }
    );
    
    const loadTime = performance.now() - startTime;
    
    // Check abort before processing data
    if (signal.aborted) {
      throw new DOMException('Aborted', 'AbortError');
    }
    
    trackLoadTime(loadTime);
    trackNetworkRequest('/api/infinite-scroll', loadTime, true, JSON.stringify(newData).length);
    
    // Enhanced data validation and integration with Zod schema validation
    if (newData.bars && newData.bars.length > 0) {
      const renderStartTime = performance.now();
      let processedBars: any[] = [];
      
      try {
        // Convert current chart data to typed format for validation
        const currentChartData: ChartData = chartData.value.map(item => ({
          time: new Date(item.time).getTime(),
          open: item.open,
          high: item.high,
          low: item.low,
          close: item.close,
          volume: item.volume
        }));
        
        // Validate incremental data with continuity checking
        const validatedIncrementalData = DataValidator.validateIncrementalData({
          bars: newData.bars,
          hasMore: newData.hasMore || true,
          direction,
          totalBars: newData.totalBars || newData.bars.length,
          metadata: {
            instrument: props.instrument,
            timeframe: props.timeframe,
            loadTime: loadTime
          }
        }, currentChartData);
        
        console.log(`✅ Incremental data validation successful: ${validatedIncrementalData.bars.length} bars validated`);
        
        // Convert validated bars to Vue format
        processedBars = validatedIncrementalData.bars.map(bar => ({
          time: bar.time, // Keep as timestamp for comparison
          timeString: new Date(bar.time).toLocaleTimeString('en-US', { 
            hour: '2-digit', 
            minute: '2-digit',
            hour12: false 
          }),
          open: bar.open,
          high: bar.high,
          low: bar.low,
          close: bar.close,
          volume: bar.volume || 0
        }));
        
      } catch (validationError) {
        if (validationError instanceof ChartDataError) {
          console.error(`💥 Incremental data validation failed (${direction}):`, validationError.message);
          
          // Attempt to sanitize and recover
          try {
            const sanitizedBars = DataValidator.sanitizeOHLCData(newData.bars);
            console.log(`🔧 Data sanitized for ${direction} load: ${sanitizedBars.length} bars recovered`);
            
            // Convert sanitized bars to Vue format
            processedBars = sanitizedBars.map(bar => ({
              time: bar.time, // Keep as timestamp for comparison
              timeString: new Date(bar.time).toLocaleTimeString('en-US', { 
                hour: '2-digit', 
                minute: '2-digit',
                hour12: false 
              }),
              open: bar.open,
              high: bar.high,
              low: bar.low,
              close: bar.close,
              volume: bar.volume || 0
            }));
            
          } catch (sanitizeError) {
            console.error(`💥 Failed to sanitize ${direction} data:`, sanitizeError);
            throw sanitizeError;
          }
        } else {
          throw validationError;
        }
      }
      
      // Check abort before DOM manipulation
      if (signal.aborted) {
        throw new DOMException('Aborted', 'AbortError');
      }
      
      // Enhanced duplicate prevention with timestamp-based filtering
      const existingTimes = new Set(chartData.value.map(item => item.time));
      const filteredBars = processedBars.filter(bar => !existingTimes.has(bar.time));
      
      if (filteredBars.length > 0) {
        if (direction === 'left') {
          chartData.value = markRaw([...filteredBars, ...chartData.value]);
        } else {
          chartData.value = markRaw([...chartData.value, ...filteredBars]);
        }
        
        // Phase 2.5: Check and update ECharts large mode after infinite scroll
        const newDatasetSize = chartData.value.length;
        if (newDatasetSize > 2000 && !echartsLargeMode.value) {
          echartsLargeMode.value = true;
          console.log(`📊 ECharts large mode ENABLED after infinite scroll: ${newDatasetSize} data points`);
        }
        
        // Check and enable performance-critical monitoring after infinite scroll
        if (newDatasetSize > manualUpdateThreshold && !performanceCriticalMode.value) {
          performanceCriticalMode.value = true;
          console.log(`⚡ Performance-critical mode ENABLED after infinite scroll: ${newDatasetSize} data points > ${manualUpdateThreshold}`);
        }
        
        // Use reactive update for performance optimization
        updateChartDataReactive();
        
        const renderTime = performance.now() - renderStartTime;
        trackRenderTime(renderTime);
        
        console.log(`Infinite scroll ${direction} completed:`, {
          newBars: filteredBars.length,
          duplicatesFiltered: processedBars.length - filteredBars.length,
          totalBars: chartData.value.length,
          loadTime: `${loadTime.toFixed(2)}ms`,
          renderTime: `${renderTime.toFixed(2)}ms`,
          healthScore: healthScore.value
        });
      } else {
        console.log(`Infinite scroll ${direction}: No new data (all duplicates filtered)`);
      }
    } else {
      console.log(`Infinite scroll ${direction}: No data received from server`);
    }
  } catch (err: any) {
    if (err.name === 'AbortError') {
      console.log(`Infinite scroll ${direction} aborted`);
      throw err;
    } else {
      const loadTime = performance.now() - startTime;
      trackNetworkRequest('/api/infinite-scroll', loadTime, false);
      trackError(err);
      throw err;
    }
  }
};

// Legacy function for backward compatibility
const loadMoreDataWithState = async (direction: 'left' | 'right') => {
  // Use the new scroll load manager for race condition prevention
  await scrollLoadManager.handleScrollLoad(direction);
};

const loadMoreData = async (direction: 'left' | 'right') => {
  const startTime = performance.now();
  
  try {
    // Validate data integrity before loading
    if (!chartData.value || chartData.value.length === 0) {
      throw new Error('No chart data available for infinite scroll');
    }
    
    const loadParams: InfiniteScrollRequestOptions = {
      instrument: props.instrument,
      timeframe: props.timeframe,
      direction,
      count: 50,
      sampling: 'adaptive'
    };
    
    // Enhanced timestamp handling with validation
    if (direction === 'left') {
      const firstItem = chartData.value[0];
      if (!firstItem?.time) {
        throw new Error('Invalid first item timestamp for historical loading');
      }
      loadParams.before_timestamp = new Date(firstItem.time).toISOString();
    } else {
      const lastItem = chartData.value[chartData.value.length - 1];
      if (!lastItem?.time) {
        throw new Error('Invalid last item timestamp for recent loading');
      }
      loadParams.after_timestamp = new Date(lastItem.time).toISOString();
    }
    
    // Use network deduplication for legacy infinite scroll as well
    const newData = await dedupedRequest(
      {
        url: `/api/infinite-scroll-legacy/${props.instrument}`,
        method: 'GET',
        params: {
          timeframe: loadParams.timeframe,
          direction: loadParams.direction,
          count: loadParams.count,
          sampling: loadParams.sampling,
          before_timestamp: loadParams.before_timestamp,
          after_timestamp: loadParams.after_timestamp
        }
      },
      async (abortSignal) => {
        return loadInfiniteScrollData(loadParams);
      }
    );
    
    const loadTime = performance.now() - startTime;
    
    trackLoadTime(loadTime);
    trackNetworkRequest('/api/infinite-scroll', loadTime, true, JSON.stringify(newData).length);
    
    // Enhanced data validation and integration
    if (newData.bars && newData.bars.length > 0) {
      const renderStartTime = performance.now();
      
      // Validate new data integrity
      const processedBars = newData.bars.map((bar: ChartData) => ({
        time: new Date(bar.time * 1000).toISOString().slice(0, 19),
        open: parseFloat(bar.open) || 0,
        high: parseFloat(bar.high) || 0,
        low: parseFloat(bar.low) || 0,
        close: parseFloat(bar.close) || 0,
        volume: parseFloat(bar.volume) || 0
      }));
      
      // Prevent duplicate data
      const existingTimes = new Set(chartData.value.map(item => item.time));
      const filteredBars = processedBars.filter(bar => !existingTimes.has(bar.time));
      
      if (filteredBars.length > 0) {
        if (direction === 'left') {
          chartData.value = markRaw([...filteredBars, ...chartData.value]);
        } else {
          chartData.value = markRaw([...chartData.value, ...filteredBars]);
        }
        
        // Use reactive update for performance optimization
        updateChartDataReactive();
        
        const renderTime = performance.now() - renderStartTime;
        trackRenderTime(renderTime);
        
        console.log(`Infinite scroll ${direction} completed:`, {
          newBars: filteredBars.length,
          duplicatesFiltered: processedBars.length - filteredBars.length,
          totalBars: chartData.value.length,
          loadTime: `${loadTime.toFixed(2)}ms`,
          renderTime: `${renderTime.toFixed(2)}ms`,
          healthScore: healthScore.value
        });
      } else {
        console.log(`Infinite scroll ${direction}: No new data (all duplicates filtered)`);
      }
    } else {
      console.log(`Infinite scroll ${direction}: No data received from server`);
    }
  } catch (err: any) {
    const loadTime = performance.now() - startTime;
    trackNetworkRequest('/api/infinite-scroll', loadTime, false);
    trackError(err);
    handleError(err, 'Infinite Scroll Loading');
  }
};

const retry = async () => {
  if (retryCount.value < maxRetries.value) {
    retryCount.value++;
    console.log(`Retrying data load (attempt ${retryCount.value}/${maxRetries.value})`);
    await new Promise(resolve => setTimeout(resolve, 1000 * retryCount.value)); // Exponential backoff
    await loadData();
  } else {
    handleError(new Error('Maximum retry attempts exceeded'), 'Retry Limit');
  }
};

// Watchers with error handling
watch([() => props.instrument, () => props.timeframe], async () => {
  try {
    retryCount.value = 0; // Reset retry count on new instrument/timeframe
    await loadData();
  } catch (err) {
    handleError(err, 'Prop Change');
  }
});

// Global error boundary
onErrorCaptured((err, instance, info) => {
  handleError(err, `Vue Error Boundary (${info})`);
  return false; // Prevent error from propagating
});

// Enhanced lifecycle with proper cleanup
onMounted(async () => {
  console.log('📊 VueEChartsChart mounted, starting data load...', {
    instrument: props.instrument,
    timeframe: props.timeframe,
    chartConfig: props.chartConfig
  });
  
  try {
    await loadData();
    
    // Force chart resize after data loads with proper timing
    await nextTick();
    
    // Wait a bit longer for Vue-ECharts to fully initialize
    setTimeout(() => {
      if (chartRef.value) {
        try {
          // Vue-ECharts exposes ECharts methods directly on the component
          if (typeof chartRef.value.resize === 'function') {
            console.log('🔧 Forcing ECharts resize after mount');
            chartRef.value.resize();
          } else {
            console.warn('⚠️ Resize method not available on chart ref');
          }
        } catch (resizeError) {
          console.warn('⚠️ Failed to resize chart after mount:', resizeError);
        }
      } else {
        console.warn('⚠️ Chart ref not ready for resize, skipping');
      }
    }, 100); // Small delay to ensure Vue-ECharts is fully ready
  } catch (err) {
    console.error('💥 Error during initial data load:', err);
    handleError(err, 'Component Mount');
  }
});

// Critical memory cleanup following Vue-ECharts best practices
onBeforeUnmount(() => {
  try {
    console.log('Vue-ECharts component unmounting - performing cleanup');
    
    // Dispose ECharts instance to prevent memory leaks
    if (chartRef.value) {
      try {
        // Vue-ECharts exposes ECharts methods directly on the component
        if (typeof chartRef.value.dispose === 'function') {
          console.log('Disposing ECharts instance');
          chartRef.value.dispose();
        } else {
          console.warn('⚠️ Dispose method not available on chart ref');
        }
      } catch (disposeError) {
        console.warn('⚠️ Failed to dispose ECharts instance:', disposeError);
      }
      
      // Clear the chart reference
      chartRef.value = null;
    }
    
    // Clear reactive data to help garbage collection
    chartData.value = markRaw([]);
    error.value = null;
    
    // Cancel all scroll operations
    scrollLoadManager.cancelAll();
    
    // Abort all pending network requests to prevent memory leaks
    abortAllRequests();
    
    // Reset scroll state
    scrollState.value = {
      isLoading: false,
      leftEdgeReached: false,
      rightEdgeReached: false,
      lastScrollTime: 0,
      scrollDebounce: 250,
      minLoadThreshold: 2,
      maxLoadThreshold: 8
    };
    
    console.log('Vue-ECharts cleanup completed successfully');
  } catch (cleanupError) {
    console.error('Error during Vue-ECharts cleanup:', cleanupError);
    // Don't throw during cleanup to prevent blocking unmount
  }
});

// Expose chart instance and optimized methods for advanced usage
defineExpose({
  chartRef,
  chartData,
  loadData,
  loadMoreData,
  updateChartDataReactive
});
</script>

<style scoped>
.vue-echarts-chart {
  position: relative;
  width: 100%;
  height: 100vh; /* Force explicit height */
  min-height: 600px; /* Ensure minimum chart height */
}

.chart {
  width: 100%;
  height: 100%;
  min-height: 600px;
  max-height: 80vh; /* Prevent excessive height */
  background: transparent; /* Ensure transparent background for canvas */
}

.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(26, 26, 26, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.error-message {
  background: #2d2d2d;
  border: 1px solid #ff4444;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  max-width: 400px;
}

.error-message h3 {
  color: #ff4444;
  margin: 0 0 1rem 0;
}

.error-message p {
  color: #cccccc;
  margin: 0 0 1.5rem 0;
}

.retry-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  background: #00d4aa;
  color: #000000;
  font-weight: bold;
  cursor: pointer;
  transition: background 0.2s;
}

.retry-btn:hover {
  background: #00b894;
}

.performance-overlay {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(45, 45, 45, 0.95);
  border: 1px solid #444;
  border-radius: 8px;
  padding: 0.75rem;
  z-index: 5;
  font-size: 0.8rem;
  min-width: 200px;
}

.performance-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.network-deduplication-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  font-size: 0.7rem;
  color: #888;
}

.metric {
  padding: 0.2rem 0.4rem;
  background: rgba(0, 0, 0, 0.4);
  border-radius: 3px;
  font-family: monospace;
}

.health-score {
  font-weight: bold;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  text-align: center;
}

.health-score.excellent {
  background: #00d4aa;
  color: #000;
}

.health-score.good {
  background: #00b894;
  color: #000;
}

.health-score.warning {
  background: #ffa500;
  color: #000;
}

.health-score.critical {
  background: #ff4444;
  color: #fff;
}

.recommendations {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.recommendation {
  color: #cccccc;
  font-size: 0.7rem;
  line-height: 1.2;
  background: rgba(0, 0, 0, 0.3);
  padding: 0.25rem;
  border-radius: 3px;
}
</style>