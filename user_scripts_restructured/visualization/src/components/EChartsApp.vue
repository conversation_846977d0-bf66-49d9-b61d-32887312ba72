<template>
  <div class="echarts-app">
    <div class="header">
      <h1>Nautilus Trader - Vue ECharts Visualization</h1>
      <div class="controls">
        <div class="instrument-selector">
          <label for="instrument">Instrument:</label>
          <select id="instrument" v-model="selectedInstrument" @change="onInstrumentChange">
            <option v-for="instrument in instruments" :key="instrument.id" :value="instrument.id || instrument.symbol || instrument">
              {{ instrument.symbol || instrument.id || instrument }}
            </option>
          </select>
        </div>
        
        <div class="timeframe-selector">
          <label for="timeframe">Timeframe:</label>
          <select id="timeframe" v-model="selectedTimeframe" @change="onTimeframeChange">
            <option value="1min">1 Minute</option>
            <option value="5min">5 Minutes</option>
            <option value="15min">15 Minutes</option>
            <option value="1hour">1 Hour</option>
            <option value="1day">1 Day</option>
          </select>
        </div>
        
        <div class="status-indicator">
          <span :class="['status', connectionStatus]">
            {{ connectionStatus.toUpperCase() }}
          </span>
        </div>
      </div>
    </div>
    
    <div class="chart-container">
      <!-- Vue-ECharts implementation (simplified - no feature flags) -->
      <VueEChartsChart
        :key="chartKey"
        :instrument="selectedInstrument"
        :timeframe="selectedTimeframe"
        :chart-config="chartConfig"
        @data-loaded="onDataLoaded"
        @error="onError"
        @scroll-to-edge="onScrollToEdge"
      />
    </div>
    
    <div class="infinite-scroll-controls">
      <button 
        @click="loadHistoricalData" 
        :disabled="!canLoadHistorical || isLoading"
        class="load-btn historical-btn"
      >
        ← Load Historical
      </button>
      
      <div class="scroll-status">
        <span v-if="isLoading" class="loading">Loading...</span>
        <span v-else class="data-info">
          {{ totalDataPoints }} bars loaded
        </span>
      </div>
      
      <button 
        @click="loadRecentData" 
        :disabled="!canLoadRecent || isLoading"
        class="load-btn recent-btn"
      >
        Load Recent →
      </button>
    </div>
    
    <div class="performance-monitor">
      <div class="memory-usage">
        <label>Memory:</label>
        <div class="memory-bar">
          <div 
            class="memory-fill" 
            :style="{ width: memoryUsage + '%' }"
            :class="memoryLevel"
          ></div>
        </div>
        <span class="memory-text">{{ memoryUsage }}%</span>
      </div>
      
      <div class="performance-stats">
        <span>Load Time: {{ lastLoadTime }}ms</span>
        <span>FPS: {{ fps }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useEChartsStore } from '../stores/echartsStore';
import { useWebSocketConnection } from '../composables/useWebSocketConnection';
import { useInfiniteScroll } from '../composables/useInfiniteScroll';
import { usePerformanceMonitor } from '../composables/usePerformanceMonitor';
import VueEChartsChart from './VueEChartsChart.vue';

// Store
const store = useEChartsStore();

// Reactive data  
const chartKey = ref(0);
// Get instrument from URL or default to MNQ.CME (which has data)
const urlInstrument = (globalThis as any).NAUTILUS_CONFIG?.instrument || 'MNQ.CME';
const selectedInstrument = ref(urlInstrument);
const selectedTimeframe = ref('1min');

// Composables
const { 
  connectionStatus, 
  connect, 
  disconnect 
} = useWebSocketConnection();

const {
  isLoading,
  canLoadHistorical,
  canLoadRecent,
  totalDataPoints,
  loadHistoricalData,
  loadRecentData
} = useInfiniteScroll(selectedInstrument, selectedTimeframe);

const {
  memoryUsage,
  memoryLevel,
  lastLoadTime,
  fps
} = usePerformanceMonitor();

// Computed
const instruments = computed(() => store.instruments);
const chartConfig = computed(() => store.chartConfig);

// Methods
const onInstrumentChange = () => {
  chartKey.value++; // Force component re-render
  store.setCurrentInstrument(selectedInstrument.value);
};

const onTimeframeChange = () => {
  chartKey.value++; // Force component re-render
  store.setCurrentTimeframe(selectedTimeframe.value);
};

const onDataLoaded = (data: any) => {
  console.log('Vue-ECharts data loaded:', data);
  // Update store with processed chart data
  if (data.processedBars && data.processedBars.length > 0) {
    store.updateChartData(data.processedBars);
  }
};

const onError = (error: any) => {
  console.error('Vue-ECharts error:', error);
  store.setError(error.message);
};

const onScrollToEdge = (direction: 'left' | 'right') => {
  console.log('Infinite scroll triggered:', direction);
  if (direction === 'left') {
    loadHistoricalData();
  } else {
    loadRecentData();
  }
};

// Lifecycle
onMounted(async () => {
  console.log('🚀 EChartsApp mounted, initializing...', {
    urlInstrument,
    selectedInstrument: selectedInstrument.value,
    selectedTimeframe: selectedTimeframe.value
  });
  
  try {
    // Initialize store
    console.log('🏪 Initializing store...');
    await store.initialize();
    console.log('✅ Store initialized successfully');
    
    // Connect to WebSocket
    console.log('🔌 Attempting WebSocket connection...');
    connect();
    
    // Set initial instrument - prefer URL parameter, fallback to first available
    console.log('📊 Available instruments:', instruments.value);
    if (instruments.value.length > 0) {
      const instrumentExists = instruments.value.some(inst => inst.id === urlInstrument || inst.symbol === urlInstrument);
      if (!instrumentExists) {
        console.warn(`⚠️ URL instrument "${urlInstrument}" not found, using first available`);
        selectedInstrument.value = instruments.value[0].id || instruments.value[0].symbol;
      }
      console.log(`🎯 Final selected instrument: ${selectedInstrument.value}`);
    } else {
      console.error('💥 No instruments available!');
    }
  } catch (error) {
    console.error('💥 Failed to initialize EChartsApp:', error);
  }
});

// Watch for connection changes
watch(connectionStatus, (status) => {
  store.setConnectionStatus(status);
});
</script>

<style scoped>
.echarts-app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: #1a1a1a;
  color: #ffffff;
}

.header {
  padding: 1rem;
  background: #2d2d2d;
  border-bottom: 1px solid #404040;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header h1 {
  margin: 0;
  font-size: 1.5rem;
  color: #00d4aa;
}

.controls {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.instrument-selector,
.timeframe-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.instrument-selector label,
.timeframe-selector label {
  font-weight: 500;
  color: #cccccc;
}

.instrument-selector select,
.timeframe-selector select {
  padding: 0.5rem;
  border: 1px solid #404040;
  border-radius: 4px;
  background: #333333;
  color: #ffffff;
  font-size: 0.9rem;
}

.status-indicator .status {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: bold;
}

.status.connected {
  background: #00d4aa;
  color: #000000;
}

.status.connecting {
  background: #ffa500;
  color: #000000;
}

.status.disconnected {
  background: #ff4444;
  color: #ffffff;
}

.chart-container {
  flex: 1;
  min-height: 600px;
  height: calc(100vh - 180px); /* Full height minus header and controls */
  position: relative;
}

.infinite-scroll-controls {
  padding: 1rem;
  background: #2d2d2d;
  border-top: 1px solid #404040;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.load-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  background: #00d4aa;
  color: #000000;
  font-weight: bold;
  cursor: pointer;
  transition: background 0.2s;
}

.load-btn:hover:not(:disabled) {
  background: #00b894;
}

.load-btn:disabled {
  background: #666666;
  color: #999999;
  cursor: not-allowed;
}

.scroll-status {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.loading {
  color: #ffa500;
  font-weight: bold;
}

.data-info {
  color: #cccccc;
}

.performance-monitor {
  padding: 0.5rem 1rem;
  background: #1a1a1a;
  border-top: 1px solid #404040;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.8rem;
}

.memory-usage {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.memory-bar {
  width: 100px;
  height: 8px;
  background: #333333;
  border-radius: 4px;
  overflow: hidden;
}

.memory-fill {
  height: 100%;
  transition: width 0.3s ease;
}

.memory-fill.low {
  background: #00d4aa;
}

.memory-fill.moderate {
  background: #ffa500;
}

.memory-fill.high {
  background: #ff4444;
}

.memory-text {
  color: #cccccc;
  font-weight: bold;
}

.performance-stats {
  display: flex;
  gap: 1rem;
  color: #cccccc;
}
</style>