/**
 * Infinite Scroll Detection Composable
 * 
 * Detects when user scrolls to chart edges and triggers
 * infinite scroll data loading with intelligent thresholds.
 */

import { ref, onUnmounted } from 'vue';
import type { ECharts } from 'echarts';

export function useInfiniteScrollDetection() {
  // State
  const chart = ref<ECharts | null>(null);
  const isEnabled = ref(true);
  const threshold = ref(5); // Percentage from edge to trigger
  const lastTriggerTime = ref(0);
  const throttleDelay = ref(1000); // Minimum time between triggers (ms)
  
  // Event handlers
  const handlers = ref<{
    onScrollToEdge?: (direction: 'left' | 'right') => void;
    onDataZoom?: (params: any) => void;
    onBrush?: (params: any) => void;
  }>({});
  
  // Detection state
  const currentZoom = ref({ start: 0, end: 100 });
  const isUserInteracting = ref(false);
  const interactionTimer = ref<number | null>(null);
  
  // Setup detection for a chart instance
  const setupScrollDetection = (
    chartInstance: ECharts,
    onScrollToEdge: (direction: 'left' | 'right') => void
  ) => {
    chart.value = chartInstance;
    handlers.value.onScrollToEdge = onScrollToEdge;
    
    // Setup event listeners
    setupDataZoomListener();
    setupBrushListener();
    setupMouseListeners();
    
    console.log('Infinite scroll detection setup complete');
  };
  
  const setupDataZoomListener = () => {
    if (!chart.value) return;
    
    chart.value.on('dataZoom', (params: any) => {
      handleDataZoomEvent(params);
    });
  };
  
  const setupBrushListener = () => {
    if (!chart.value) return;
    
    chart.value.on('brush', (params: any) => {
      handleBrushEvent(params);
    });
  };
  
  const setupMouseListeners = () => {
    if (!chart.value) return;
    
    // Track user interaction state
    chart.value.on('mousedown', () => {
      isUserInteracting.value = true;
      
      if (interactionTimer.value) {
        clearTimeout(interactionTimer.value);
      }
    });
    
    chart.value.on('mouseup', () => {
      // Delay setting interaction to false to capture zoom end
      interactionTimer.value = window.setTimeout(() => {
        isUserInteracting.value = false;
      }, 100);
    });
    
    // Track mouse wheel for zoom detection
    chart.value.on('globalout', () => {
      isUserInteracting.value = false;
    });
  };
  
  const handleDataZoomEvent = (params: any) => {
    if (!isEnabled.value || !handlers.value.onScrollToEdge) return;
    
    // Update current zoom state
    if (params.start !== undefined && params.end !== undefined) {
      currentZoom.value = {
        start: params.start,
        end: params.end
      };
    }
    
    // Only trigger on user interaction, not programmatic zoom
    if (!isUserInteracting.value) return;
    
    // Throttle trigger calls
    const now = Date.now();
    if (now - lastTriggerTime.value < throttleDelay.value) {
      return;
    }
    
    const { start, end } = currentZoom.value;
    
    // Check if we're near the left edge (historical data)
    if (start <= threshold.value) {
      console.log('Triggered infinite scroll: left edge', { start, threshold: threshold.value });
      lastTriggerTime.value = now;
      handlers.value.onScrollToEdge('left');
    }
    
    // Check if we're near the right edge (recent data)
    else if (end >= (100 - threshold.value)) {
      console.log('Triggered infinite scroll: right edge', { end, threshold: threshold.value });
      lastTriggerTime.value = now;
      handlers.value.onScrollToEdge('right');
    }
    
    // Call custom dataZoom handler if provided
    if (handlers.value.onDataZoom) {
      handlers.value.onDataZoom(params);
    }
  };
  
  const handleBrushEvent = (params: any) => {
    // Handle brush selection events if needed
    if (handlers.value.onBrush) {
      handlers.value.onBrush(params);
    }
  };
  
  // Mouse position based detection (alternative approach)
  const detectScrollToEdge = (event: MouseEvent) => {
    if (!chart.value || !isEnabled.value) return;
    
    const chartDom = chart.value.getDom();
    if (!chartDom) return;
    
    const rect = chartDom.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const width = rect.width;
    
    const leftEdgeThreshold = width * (threshold.value / 100);
    const rightEdgeThreshold = width * ((100 - threshold.value) / 100);
    
    // Check proximity to edges
    if (x <= leftEdgeThreshold && handlers.value.onScrollToEdge) {
      const now = Date.now();
      if (now - lastTriggerTime.value >= throttleDelay.value) {
        lastTriggerTime.value = now;
        handlers.value.onScrollToEdge('left');
      }
    } else if (x >= rightEdgeThreshold && handlers.value.onScrollToEdge) {
      const now = Date.now();
      if (now - lastTriggerTime.value >= throttleDelay.value) {
        lastTriggerTime.value = now;
        handlers.value.onScrollToEdge('right');
      }
    }
  };
  
  // Advanced detection methods
  const detectZoomLevel = () => {
    if (!chart.value) return { start: 0, end: 100 };
    
    try {
      const option = chart.value.getOption();
      const dataZoom = Array.isArray(option.dataZoom) ? option.dataZoom[0] : option.dataZoom;
      
      if (dataZoom) {
        return {
          start: dataZoom.start || 0,
          end: dataZoom.end || 100
        };
      }
    } catch (error) {
      console.warn('Failed to get zoom level:', error);
    }
    
    return currentZoom.value;
  };
  
  const isAtEdge = (direction: 'left' | 'right') => {
    const zoom = detectZoomLevel();
    
    if (direction === 'left') {
      return zoom.start <= threshold.value;
    } else {
      return zoom.end >= (100 - threshold.value);
    }
  };
  
  const getDistanceFromEdge = (direction: 'left' | 'right') => {
    const zoom = detectZoomLevel();
    
    if (direction === 'left') {
      return zoom.start;
    } else {
      return 100 - zoom.end;
    }
  };
  
  // Programmatic zoom control
  const zoomToPercentage = (start: number, end: number) => {
    if (!chart.value) return;
    
    chart.value.setOption({
      dataZoom: [{
        start,
        end
      }]
    });
    
    currentZoom.value = { start, end };
  };
  
  const zoomIn = (factor: number = 0.8) => {
    const zoom = detectZoomLevel();
    const range = zoom.end - zoom.start;
    const newRange = range * factor;
    const center = (zoom.start + zoom.end) / 2;
    
    const newStart = Math.max(0, center - newRange / 2);
    const newEnd = Math.min(100, center + newRange / 2);
    
    zoomToPercentage(newStart, newEnd);
  };
  
  const zoomOut = (factor: number = 1.25) => {
    const zoom = detectZoomLevel();
    const range = zoom.end - zoom.start;
    const newRange = Math.min(100, range * factor);
    const center = (zoom.start + zoom.end) / 2;
    
    const newStart = Math.max(0, center - newRange / 2);
    const newEnd = Math.min(100, center + newRange / 2);
    
    zoomToPercentage(newStart, newEnd);
  };
  
  const resetZoom = () => {
    zoomToPercentage(0, 100);
  };
  
  // Configuration methods
  const setThreshold = (newThreshold: number) => {
    threshold.value = Math.max(0, Math.min(50, newThreshold));
  };
  
  const setThrottleDelay = (delay: number) => {
    throttleDelay.value = Math.max(100, delay);
  };
  
  const enable = () => {
    isEnabled.value = true;
  };
  
  const disable = () => {
    isEnabled.value = false;
  };
  
  const toggle = () => {
    isEnabled.value = !isEnabled.value;
  };
  
  // Manual trigger for testing
  const triggerScrollToEdge = (direction: 'left' | 'right') => {
    if (handlers.value.onScrollToEdge) {
      handlers.value.onScrollToEdge(direction);
    }
  };
  
  // Cleanup
  const cleanupScrollDetection = () => {
    if (chart.value) {
      // Remove all event listeners
      chart.value.off('dataZoom');
      chart.value.off('brush');
      chart.value.off('mousedown');
      chart.value.off('mouseup');
      chart.value.off('globalout');
    }
    
    if (interactionTimer.value) {
      clearTimeout(interactionTimer.value);
    }
    
    chart.value = null;
    handlers.value = {};
    
    console.log('Infinite scroll detection cleanup complete');
  };
  
  // Lifecycle
  onUnmounted(() => {
    cleanupScrollDetection();
  });
  
  return {
    // State
    isEnabled,
    threshold,
    currentZoom,
    isUserInteracting,
    
    // Setup
    setupScrollDetection,
    cleanupScrollDetection,
    
    // Detection
    detectScrollToEdge,
    detectZoomLevel,
    isAtEdge,
    getDistanceFromEdge,
    
    // Control
    zoomToPercentage,
    zoomIn,
    zoomOut,
    resetZoom,
    
    // Configuration
    setThreshold,
    setThrottleDelay,
    enable,
    disable,
    toggle,
    
    // Testing
    triggerScrollToEdge
  };
}