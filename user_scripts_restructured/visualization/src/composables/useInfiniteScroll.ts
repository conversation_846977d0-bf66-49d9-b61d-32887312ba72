/**
 * Infinite Scroll Composable
 * 
 * Manages bidirectional infinite scrolling for chart data with
 * intelligent loading and memory management.
 */

import { ref, computed, watch } from 'vue';
import { useEChartsStore } from '../stores/echartsStore';
import { useWebSocketConnection } from './useWebSocketConnection';
import type { InfiniteScrollRequestOptions } from '../types/infinite-scroll';

export function useInfiniteScroll(instrument: any, timeframe: any) {
  const store = useEChartsStore();
  const { requestInfiniteScrollData, isConnected } = useWebSocketConnection();
  
  // State
  const isLoading = ref(false);
  const loadingDirection = ref<'left' | 'right' | null>(null);
  const loadQueue = ref<Array<{ direction: 'left' | 'right'; priority: number }>>([]);
  const lastLoadTime = ref(0);
  const loadThrottle = ref(500); // Minimum time between loads (ms)
  
  // Computed
  const canLoadHistorical = computed(() => 
    !isLoading.value && 
    store.infiniteScrollState.hasMoreHistorical && 
    isConnected.value
  );
  
  const canLoadRecent = computed(() => 
    !isLoading.value && 
    store.infiniteScrollState.hasMoreRecent && 
    isConnected.value
  );
  
  const totalDataPoints = computed(() => 
    store.infiniteScrollState.totalBarsLoaded
  );
  
  // Load throttling
  const canLoad = computed(() => {
    const now = Date.now();
    return now - lastLoadTime.value >= loadThrottle.value;
  });
  
  // Methods
  const loadHistoricalData = async () => {
    if (!canLoadHistorical.value || !canLoad.value) {
      return;
    }
    
    await performLoad('left');
  };
  
  const loadRecentData = async () => {
    if (!canLoadRecent.value || !canLoad.value) {
      return;
    }
    
    await performLoad('right');
  };
  
  const performLoad = async (direction: 'left' | 'right') => {
    if (isLoading.value) {
      // Queue the load request
      queueLoad(direction);
      return;
    }
    
    try {
      isLoading.value = true;
      loadingDirection.value = direction;
      lastLoadTime.value = Date.now();
      
      store.setLoadingState(true, direction);
      
      const loadOptions: InfiniteScrollRequestOptions = {
        instrument: instrument.value,
        timeframe: timeframe.value,
        direction,
        count: store.chartConfig.loadChunkSize,
        sampling: store.chartConfig.samplingStrategy
      };
      
      // Add timestamp filters based on direction
      if (direction === 'left' && store.infiniteScrollState.oldestTimestamp) {
        loadOptions.before_timestamp = new Date(store.infiniteScrollState.oldestTimestamp).toISOString();
      } else if (direction === 'right' && store.infiniteScrollState.newestTimestamp) {
        loadOptions.after_timestamp = new Date(store.infiniteScrollState.newestTimestamp).toISOString();
      }
      
      // Record performance metrics
      const startTime = performance.now();
      
      // Request data via WebSocket
      requestInfiniteScrollData(loadOptions);
      
      // Note: Actual data handling is done in WebSocket event handlers
      // This is just the request trigger
      
      const endTime = performance.now();
      const loadTime = endTime - startTime;
      store.recordLoadTime(loadTime);
      
    } catch (error) {
      console.error(`Failed to load ${direction} data:`, error);
      store.setError(`Failed to load ${direction} data`);
    } finally {
      isLoading.value = false;
      loadingDirection.value = null;
      store.setLoadingState(false);
      
      // Process queued loads
      processLoadQueue();
    }
  };
  
  const queueLoad = (direction: 'left' | 'right', priority: number = 1) => {
    // Avoid duplicate queue entries
    const existingIndex = loadQueue.value.findIndex(item => item.direction === direction);
    if (existingIndex >= 0) {
      // Update priority if higher
      if (loadQueue.value[existingIndex].priority < priority) {
        loadQueue.value[existingIndex].priority = priority;
      }
      return;
    }
    
    loadQueue.value.push({ direction, priority });
    
    // Sort by priority (higher priority first)
    loadQueue.value.sort((a, b) => b.priority - a.priority);
  };
  
  const processLoadQueue = () => {
    if (loadQueue.value.length === 0 || isLoading.value) {
      return;
    }
    
    const nextLoad = loadQueue.value.shift();
    if (nextLoad) {
      performLoad(nextLoad.direction);
    }
  };
  
  const clearLoadQueue = () => {
    loadQueue.value = [];
  };
  
  // Auto-loading based on scroll position
  const checkAutoLoad = (scrollPosition: { start: number; end: number }) => {
    const { start, end } = scrollPosition;
    const threshold = 10; // Percentage from edge to trigger load
    
    // Check if we're near the left edge (historical data)
    if (start <= threshold && canLoadHistorical.value) {
      queueLoad('left', 2); // Higher priority for user-initiated scroll
    }
    
    // Check if we're near the right edge (recent data)
    if (end >= (100 - threshold) && canLoadRecent.value) {
      queueLoad('right', 2); // Higher priority for user-initiated scroll
    }
    
    // Process queue
    if (!isLoading.value) {
      processLoadQueue();
    }
  };
  
  // Memory management
  const checkMemoryPressure = () => {
    const memoryUsage = store.performanceMetrics.memoryUsage;
    const threshold = store.chartConfig.memoryThreshold;
    
    if (memoryUsage > threshold) {
      console.warn(`Memory usage (${memoryUsage}%) exceeds threshold (${threshold}%)`);
      
      // Trigger data trimming
      store.trimChartData();
      
      // Reduce load chunk size temporarily
      const originalChunkSize = store.chartConfig.loadChunkSize;
      store.updateChartConfig({
        loadChunkSize: Math.max(50, Math.floor(originalChunkSize * 0.8))
      });
      
      // Restore chunk size after a delay
      setTimeout(() => {
        store.updateChartConfig({
          loadChunkSize: originalChunkSize
        });
      }, 10000);
    }
  };
  
  // Smart loading strategies
  const getOptimalChunkSize = (direction: 'left' | 'right') => {
    const baseSize = store.chartConfig.loadChunkSize;
    const memoryUsage = store.performanceMetrics.memoryUsage;
    const averageLoadTime = store.performanceMetrics.averageLoadTime;
    
    // Reduce chunk size if memory usage is high
    if (memoryUsage > 75) {
      return Math.floor(baseSize * 0.6);
    }
    
    // Reduce chunk size if load times are slow
    if (averageLoadTime > 2000) {
      return Math.floor(baseSize * 0.8);
    }
    
    // Increase chunk size for historical loads (usually less frequent)
    if (direction === 'left' && memoryUsage < 50 && averageLoadTime < 1000) {
      return Math.floor(baseSize * 1.2);
    }
    
    return baseSize;
  };
  
  const getOptimalSamplingStrategy = () => {
    const dataPoints = store.infiniteScrollState.totalBarsLoaded;
    const maxPoints = store.chartConfig.maxDataPoints;
    
    // Use adaptive sampling when approaching data limits
    if (dataPoints > maxPoints * 0.8) {
      return 'adaptive';
    }
    
    // Use time-based sampling for large datasets
    if (dataPoints > maxPoints * 0.6) {
      return 'time_based';
    }
    
    // Use uniform sampling for medium datasets
    if (dataPoints > maxPoints * 0.4) {
      return 'uniform';
    }
    
    // No sampling for small datasets
    return 'none';
  };
  
  // Preload strategies
  const preloadAdjacentData = () => {
    if (!isConnected.value || isLoading.value) {
      return;
    }
    
    // Preload a small amount of historical data
    if (canLoadHistorical.value) {
      queueLoad('left', 0); // Low priority preload
    }
    
    // Preload a small amount of recent data
    if (canLoadRecent.value) {
      queueLoad('right', 0); // Low priority preload
    }
  };
  
  // Reset functionality
  const reset = () => {
    isLoading.value = false;
    loadingDirection.value = null;
    clearLoadQueue();
    store.resetChartData();
  };
  
  // Watch for instrument/timeframe changes
  watch([instrument, timeframe], () => {
    reset();
  });
  
  // Performance monitoring
  watch(() => store.performanceMetrics.memoryUsage, () => {
    checkMemoryPressure();
  });
  
  return {
    // State
    isLoading,
    loadingDirection,
    
    // Computed
    canLoadHistorical,
    canLoadRecent,
    totalDataPoints,
    
    // Methods
    loadHistoricalData,
    loadRecentData,
    checkAutoLoad,
    queueLoad,
    clearLoadQueue,
    reset,
    preloadAdjacentData,
    
    // Advanced methods
    getOptimalChunkSize,
    getOptimalSamplingStrategy
  };
}