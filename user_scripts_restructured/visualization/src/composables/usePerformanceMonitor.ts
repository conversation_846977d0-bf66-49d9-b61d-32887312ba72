/**
 * Performance Monitor Composable
 * 
 * Monitors and tracks performance metrics for the chart application
 * including memory usage, FPS, load times, and system health.
 */

import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useEChartsStore } from '../stores/echartsStore';

export function usePerformanceMonitor() {
  const store = useEChartsStore();
  
  // State
  const isMonitoring = ref(false);
  const monitoringInterval = ref<number | null>(null);
  const fpsCounter = ref(0);
  const fpsInterval = ref<number | null>(null);
  const lastFrameTime = ref(0);
  const frameCount = ref(0);
  
  // Performance observer
  const performanceObserver = ref<PerformanceObserver | null>(null);
  
  // Memory tracking
  const memoryInfo = ref({
    used: 0,
    total: 0,
    percentage: 0
  });
  
  // Load time tracking
  const loadTimes = ref<number[]>([]);
  const maxLoadTimeHistory = 100;
  
  // Computed
  const memoryUsage = computed(() => memoryInfo.value.percentage);
  const memoryLevel = computed(() => {
    const usage = memoryUsage.value;
    if (usage < 50) return 'low';
    if (usage < 75) return 'moderate';
    return 'high';
  });
  
  const lastLoadTime = computed(() => store.performanceMetrics.lastLoadTime);
  const fps = computed(() => store.performanceMetrics.fps);
  const averageLoadTime = computed(() => {
    if (loadTimes.value.length === 0) return 0;
    return loadTimes.value.reduce((sum, time) => sum + time, 0) / loadTimes.value.length;
  });
  
  // Methods
  const startMonitoring = () => {
    if (isMonitoring.value) return;
    
    isMonitoring.value = true;
    
    // Start memory monitoring
    startMemoryMonitoring();
    
    // Start FPS monitoring
    startFPSMonitoring();
    
    // Start performance observation
    startPerformanceObservation();
    
    console.log('Performance monitoring started');
  };
  
  const stopMonitoring = () => {
    if (!isMonitoring.value) return;
    
    isMonitoring.value = false;
    
    // Stop intervals
    if (monitoringInterval.value) {
      clearInterval(monitoringInterval.value);
      monitoringInterval.value = null;
    }
    
    if (fpsInterval.value) {
      clearInterval(fpsInterval.value);
      fpsInterval.value = null;
    }
    
    // Stop performance observer
    if (performanceObserver.value) {
      performanceObserver.value.disconnect();
      performanceObserver.value = null;
    }
    
    console.log('Performance monitoring stopped');
  };
  
  const startMemoryMonitoring = () => {
    const updateMemoryInfo = () => {
      if (typeof window !== 'undefined' && 'performance' in window) {
        // Modern browsers with Memory API
        if ('memory' in performance) {
          const memory = (performance as any).memory;
          const used = memory.usedJSHeapSize;
          const total = memory.totalJSHeapSize;
          const limit = memory.jsHeapSizeLimit;
          
          memoryInfo.value = {
            used,
            total,
            percentage: Math.round((used / limit) * 100)
          };
          
          store.updateMemoryUsage(memoryInfo.value.percentage);
        } else {
          // Fallback: estimate based on data size
          const dataSize = estimateDataMemoryUsage();
          const estimatedTotal = 100 * 1024 * 1024; // Assume 100MB available
          
          memoryInfo.value = {
            used: dataSize,
            total: estimatedTotal,
            percentage: Math.round((dataSize / estimatedTotal) * 100)
          };
          
          store.updateMemoryUsage(memoryInfo.value.percentage);
        }
      }
    };
    
    // Initial measurement
    updateMemoryInfo();
    
    // Regular monitoring
    monitoringInterval.value = window.setInterval(updateMemoryInfo, 2000);
  };
  
  const estimateDataMemoryUsage = () => {
    const chartData = store.chartData;
    if (!chartData.length) return 0;
    
    // Rough estimation: each data point is ~100 bytes
    const dataPointSize = 100;
    return chartData.length * dataPointSize;
  };
  
  const startFPSMonitoring = () => {
    let lastTime = performance.now();
    let frameCount = 0;
    
    const measureFPS = () => {
      const currentTime = performance.now();
      frameCount++;
      
      if (currentTime - lastTime >= 1000) {
        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
        store.updateFPS(fps);
        
        frameCount = 0;
        lastTime = currentTime;
      }
      
      if (isMonitoring.value) {
        requestAnimationFrame(measureFPS);
      }
    };
    
    requestAnimationFrame(measureFPS);
  };
  
  const startPerformanceObservation = () => {
    if (!('PerformanceObserver' in window)) {
      console.warn('PerformanceObserver not supported');
      return;
    }
    
    try {
      performanceObserver.value = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          handlePerformanceEntry(entry);
        }
      });
      
      // Observe different types of performance entries
      performanceObserver.value.observe({ 
        entryTypes: ['measure', 'navigation', 'resource', 'paint'] 
      });
    } catch (error) {
      console.warn('Failed to start performance observation:', error);
    }
  };
  
  const handlePerformanceEntry = (entry: PerformanceEntry) => {
    switch (entry.entryType) {
      case 'measure':
        if (entry.name.includes('chart') || entry.name.includes('load')) {
          recordLoadTime(entry.duration);
        }
        break;
        
      case 'navigation':
        const navEntry = entry as PerformanceNavigationTiming;
        const pageLoadTime = navEntry.loadEventEnd - (navEntry as any).navigationStart;
        console.log('Page load time:', pageLoadTime);
        break;
        
      case 'resource':
        const resourceEntry = entry as PerformanceResourceTiming;
        if (resourceEntry.name.includes('api/') || resourceEntry.name.includes('echarts')) {
          const loadTime = resourceEntry.responseEnd - resourceEntry.requestStart;
          recordLoadTime(loadTime);
        }
        break;
        
      case 'paint':
        console.log(`${entry.name}: ${entry.startTime}`);
        break;
    }
  };
  
  const recordLoadTime = (duration: number) => {
    loadTimes.value.push(duration);
    
    // Keep only recent load times
    if (loadTimes.value.length > maxLoadTimeHistory) {
      loadTimes.value = loadTimes.value.slice(-maxLoadTimeHistory);
    }
    
    store.recordLoadTime(duration);
  };
  
  // Performance measurement utilities
  const measurePerformance = (name: string, fn: () => Promise<any>) => {
    return new Promise(async (resolve, reject) => {
      const startTime = performance.now();
      performance.mark(`${name}-start`);
      
      try {
        const result = await fn();
        
        const endTime = performance.now();
        performance.mark(`${name}-end`);
        performance.measure(name, `${name}-start`, `${name}-end`);
        
        const duration = endTime - startTime;
        recordLoadTime(duration);
        
        resolve(result);
      } catch (error) {
        reject(error);
      } finally {
        // Clean up marks
        performance.clearMarks(`${name}-start`);
        performance.clearMarks(`${name}-end`);
      }
    });
  };
  
  const measureSync = (name: string, fn: () => any) => {
    const startTime = performance.now();
    performance.mark(`${name}-start`);
    
    try {
      const result = fn();
      
      const endTime = performance.now();
      performance.mark(`${name}-end`);
      performance.measure(name, `${name}-start`, `${name}-end`);
      
      const duration = endTime - startTime;
      recordLoadTime(duration);
      
      return result;
    } finally {
      // Clean up marks
      performance.clearMarks(`${name}-start`);
      performance.clearMarks(`${name}-end`);
    }
  };
  
  // Health check
  const getHealthStatus = () => {
    const memory = memoryUsage.value;
    const currentFPS = fps.value;
    const avgLoadTime = averageLoadTime.value;
    
    let status = 'good';
    const issues = [];
    
    if (memory > 85) {
      status = 'critical';
      issues.push('High memory usage');
    } else if (memory > 70) {
      status = 'warning';
      issues.push('Moderate memory usage');
    }
    
    if (currentFPS < 30) {
      status = status === 'critical' ? 'critical' : 'warning';
      issues.push('Low FPS');
    }
    
    if (avgLoadTime > 3000) {
      status = status === 'critical' ? 'critical' : 'warning';
      issues.push('Slow load times');
    }
    
    return {
      status,
      issues,
      metrics: {
        memory,
        fps: currentFPS,
        averageLoadTime: avgLoadTime,
        totalRequests: store.performanceMetrics.totalRequests,
        errorCount: store.performanceMetrics.errorCount
      }
    };
  };
  
  // Export performance data
  const exportPerformanceData = () => {
    const health = getHealthStatus();
    const data = {
      timestamp: new Date().toISOString(),
      health,
      loadTimes: [...loadTimes.value],
      memoryHistory: [memoryInfo.value],
      storeMetrics: { ...store.performanceMetrics }
    };
    
    return data;
  };
  
  // Lifecycle
  onMounted(() => {
    startMonitoring();
  });
  
  onUnmounted(() => {
    stopMonitoring();
  });
  
  return {
    // State
    isMonitoring,
    memoryInfo,
    
    // Computed
    memoryUsage,
    memoryLevel,
    lastLoadTime,
    fps,
    averageLoadTime,
    
    // Methods
    startMonitoring,
    stopMonitoring,
    measurePerformance,
    measureSync,
    getHealthStatus,
    exportPerformanceData,
    recordLoadTime
  };
}