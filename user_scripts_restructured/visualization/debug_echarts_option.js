// Debug script to test ECharts option structure
// This helps identify if the chart option is properly formatted

// First, let's test a minimal working ECharts candlestick option
const testCandlestickOption = {
  title: {
    text: 'Test Candlestick Chart',
    left: 'center'
  },
  xAxis: {
    type: 'category',
    data: ['2024-01-01', '2024-01-02', '2024-01-03']
  },
  yAxis: {
    type: 'value'
  },
  series: [{
    type: 'candlestick',
    data: [
      [20, 30, 10, 25],  // [open, close, low, high]
      [25, 35, 15, 30],
      [30, 25, 20, 35]
    ]
  }]
};

console.log('Test candlestick option:');
console.log(JSON.stringify(testCandlestickOption, null, 2));

// Now let's create a structure based on what we saw in console logs
const sampleData = [
  {
    "close": 21888.75,
    "high": 21897.5,
    "low": 21877.5,
    "open": 21879,
    "time": 1749842400
  },
  {
    "close": 21876.25,
    "high": 21897.5,
    "low": 21875.75,
    "open": 21891,
    "time": 1749842460
  }
];

// Convert to ECharts format
const candlestickData = sampleData.map(item => [
  item.open, item.close, item.low, item.high
]);

const times = sampleData.map(item => new Date(item.time * 1000).toISOString());

const chartOption = {
  backgroundColor: '#1a1a1a',
  title: {
    text: 'MNQ.CME - 1min',
    textStyle: { color: '#ffffff' },
    left: 'center'
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: { type: 'cross' }
  },
  xAxis: {
    type: 'category',
    data: times,
    axisLabel: { color: '#cccccc' }
  },
  yAxis: {
    type: 'value',
    axisLabel: { color: '#cccccc' }
  },
  series: [{
    name: 'OHLC',
    type: 'candlestick',
    data: candlestickData,
    itemStyle: {
      color: '#00d4aa',
      color0: '#ff6b6b',
      borderColor: '#00d4aa',
      borderColor0: '#ff6b6b'
    }
  }]
};

console.log('\nGenerated chart option:');
console.log(JSON.stringify(chartOption, null, 2));