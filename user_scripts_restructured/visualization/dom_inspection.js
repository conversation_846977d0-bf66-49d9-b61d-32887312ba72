// DOM inspection via console
// Check if chart container exists and has proper dimensions
const app = document.getElementById('app');
console.log('App element:', app?.offsetWidth, 'x', app?.offsetHeight);

// Look for Vue-ECharts containers
const vueEChartsContainer = document.querySelector('.vue-echarts-chart');
console.log('Vue-ECharts container:', vueEChartsContainer?.offsetWidth, 'x', vueEChartsContainer?.offsetHeight);

const chartContainer = document.querySelector('.chart');
console.log('Chart container:', chartContainer?.offsetWidth, 'x', chartContainer?.offsetHeight);

// Look for ECharts instances
const echartsElements = document.querySelectorAll('[_echarts_instance_]');
console.log('ECharts instances found:', echartsElements.length);

echartsElements.forEach((el, i) => {
    console.log(`ECharts ${i}:`, {
        width: el.offsetWidth,
        height: el.offsetHeight,
        hasCanvas: !!el.querySelector('canvas'),
        hasSvg: !!el.querySelector('svg'),
        style: el.getAttribute('style')
    });
});

// Look for canvas elements
const canvases = document.querySelectorAll('canvas');
console.log('Canvas elements:', canvases.length);

canvases.forEach((canvas, i) => {
    console.log(`Canvas ${i}:`, {
        width: canvas.width,
        height: canvas.height,
        offsetWidth: canvas.offsetWidth,
        offsetHeight: canvas.offsetHeight,
        style: canvas.getAttribute('style'),
        parent: canvas.parentElement?.className
    });
});

// Check computed styles
if (chartContainer) {
    const computed = window.getComputedStyle(chartContainer);
    console.log('Chart container computed styles:', {
        width: computed.width,
        height: computed.height,
        display: computed.display,
        visibility: computed.visibility,
        opacity: computed.opacity
    });
}