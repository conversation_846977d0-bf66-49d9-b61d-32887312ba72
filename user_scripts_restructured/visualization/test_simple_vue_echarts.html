<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">
    <title>Simple Vue-ECharts Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: #ffffff;
            margin: 0;
            padding: 20px;
        }
        .chart {
            height: 400px;
            width: 100%;
            border: 2px solid #00d4aa;
        }
    </style>
</head>
<body>
    <h1>Simple Vue-ECharts Test</h1>
    <div id="app">
        <v-chart class="chart" :option="option" />
    </div>

    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@next"></script>
    <!-- ECharts -->
    <script src="https://unpkg.com/echarts@5.4.3/dist/echarts.min.js"></script>
    <!-- Vue-ECharts -->
    <script src="https://unpkg.com/vue-echarts@7.0.3/dist/index.umd.min.js"></script>

    <script>
        const { createApp, ref } = Vue;

        // Register Vue-ECharts globally
        const app = createApp({
            setup() {
                const option = ref({
                    title: {
                        text: 'Simple Test Chart',
                        left: 'center',
                        textStyle: { color: '#ffffff' }
                    },
                    backgroundColor: '#1a1a1a',
                    xAxis: {
                        type: 'category',
                        data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri'],
                        axisLabel: { color: '#cccccc' }
                    },
                    yAxis: {
                        type: 'value',
                        axisLabel: { color: '#cccccc' }
                    },
                    series: [{
                        data: [120, 200, 150, 80, 70],
                        type: 'line',
                        lineStyle: { color: '#00d4aa' },
                        itemStyle: { color: '#00d4aa' }
                    }]
                });

                return { option };
            }
        });

        app.component('v-chart', VueECharts);
        app.mount('#app');
    </script>
</body>
</html>