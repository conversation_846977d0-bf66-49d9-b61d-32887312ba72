import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import { resolve } from 'node:path';
import { fileURLToPath } from 'node:url';

const __dirname = fileURLToPath(new URL('.', import.meta.url));

export default defineConfig({
  plugins: [vue()],
  build: {
    outDir: 'vue_echarts/static/js',
    emptyOutDir: true,
    lib: {
      entry: resolve(__dirname, 'src/vue-echarts-app.ts'),
      name: 'VueEChartsApp',
      fileName: 'vue-echarts-app',
      formats: ['es']
    },
    rollupOptions: {
      output: {
        format: 'es',
        entryFileNames: '[name].js',
        preserveModules: false,
        exports: 'named'
      }
    }
  },
  server: {
    port: 5173,
    host: '0.0.0.0'
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  }
});