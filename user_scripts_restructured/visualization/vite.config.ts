import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import { resolve } from 'node:path';
import { fileURLToPath } from 'node:url';

const __dirname = fileURLToPath(new URL('.', import.meta.url));

export default defineConfig({
  plugins: [vue()],
  build: {
    outDir: 'vue_echarts/static/js',
    emptyOutDir: true,
    rollupOptions: {
      input: {
        'vue-echarts-app': resolve(__dirname, 'src/vue-echarts-app.ts')
      },
      output: {
        format: 'es',
        entryFileNames: '[name].js'
      }
    }
  },
  server: {
    port: 5173,
    host: '0.0.0.0'
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  }
});