#!/usr/bin/env python3
"""
Debug script to examine chart DOM structure and rendering status
"""
import asyncio
from playwright.async_api import async_playwright
import json

async def debug_chart_dom():
    async with async_playwright() as p:
        browser = await p.chromium.launch()
        page = await browser.new_page()
        
        # Navigate to the chart page
        await page.goto('http://localhost:8082/chart/MNQ.CME')
        await page.wait_for_timeout(3000)  # Wait for app to load
        
        print("=== DOM STRUCTURE ANALYSIS ===")
        
        # Check for chart containers
        chart_containers = await page.evaluate("""
            () => {
                const containers = [];
                const selectors = [
                    '[id*="chart"]',
                    '[class*="chart"]', 
                    '[class*="echarts"]',
                    'canvas',
                    'svg',
                    '.vue-echarts-chart',
                    '#chart-container',
                    '[ref="chartRef"]'
                ];
                
                selectors.forEach(selector => {
                    try {
                        const elements = document.querySelectorAll(selector);
                        elements.forEach(el => {
                            containers.push({
                                selector,
                                tagName: el.tagName,
                                id: el.id,
                                className: el.className,
                                clientWidth: el.clientWidth,
                                clientHeight: el.clientHeight,
                                hasChildren: el.children.length > 0,
                                innerHTML: el.innerHTML.substring(0, 100)
                            });
                        });
                    } catch (e) {
                        console.log('Error with selector:', selector, e);
                    }
                });
                
                return containers;
            }
        """)
        
        print("Chart containers found:")
        for container in chart_containers:
            print(f"  - {container['selector']}: {container['tagName']} (id: {container['id']}, class: {container['className']})")
            print(f"    Size: {container['clientWidth']}x{container['clientHeight']}")
            print(f"    Has children: {container['hasChildren']}")
            print(f"    Content preview: {container['innerHTML'][:50]}...")
            print()
        
        # Check Vue app mounting
        vue_status = await page.evaluate("""
            () => {
                const app = document.querySelector('#app');
                return {
                    appExists: !!app,
                    appContent: app ? app.innerHTML.substring(0, 200) : null,
                    appChildren: app ? app.children.length : 0,
                    hasVueInstance: window.Vue !== undefined,
                    hasECharts: window.echarts !== undefined
                };
            }
        """)
        
        print("=== VUE APP STATUS ===")
        print(f"App container exists: {vue_status['appExists']}")
        print(f"App children count: {vue_status['appChildren']}")
        print(f"Vue available: {vue_status['hasVueInstance']}")
        print(f"ECharts available: {vue_status['hasECharts']}")
        print(f"App content preview: {vue_status['appContent'][:100] if vue_status['appContent'] else 'None'}...")
        print()
        
        # Check console errors
        console_messages = []
        page.on('console', lambda msg: console_messages.append(f"{msg.type}: {msg.text}"))
        
        await page.reload()
        await page.wait_for_timeout(3000)
        
        print("=== CONSOLE MESSAGES ===")
        for msg in console_messages[-10:]:  # Last 10 messages
            print(f"  {msg}")
        
        await browser.close()

if __name__ == "__main__":
    asyncio.run(debug_chart_dom())
