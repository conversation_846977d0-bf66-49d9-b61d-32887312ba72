<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue.js Loading Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: #ffffff;
            padding: 20px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background: #2d5a2d;
            border: 1px solid #4caf50;
        }
        .error {
            background: #5a2d2d;
            border: 1px solid #f44336;
        }
        .info {
            background: #2d4a5a;
            border: 1px solid #2196f3;
        }
        .loading {
            background: #5a5a2d;
            border: 1px solid #ff9800;
        }
        pre {
            background: #333;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Vue.js Application Loading Test</h1>
    <div id="test-results"></div>
    
    <script type="module">
        const results = document.getElementById('test-results');
        
        function addResult(type, title, message, details = null) {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `
                <h3>${title}</h3>
                <p>${message}</p>
                ${details ? `<pre>${details}</pre>` : ''}
            `;
            results.appendChild(div);
        }
        
        addResult('info', 'Test Started', 'Testing Vue.js module loading...');
        
        try {
            // Test 1: Check if we can import the module
            addResult('loading', 'Test 1', 'Attempting to import Vue.js module...');
            
            const moduleUrl = '/static/js/vue-echarts-app.js?v=' + Date.now();
            console.log('Importing module from:', moduleUrl);
            
            import(moduleUrl)
                .then(module => {
                    addResult('success', 'Test 1 - SUCCESS', 'Vue.js module loaded successfully!');
                    addResult('info', 'Module Exports', 'Available exports: ' + Object.keys(module).join(', '));
                    
                    // Test 2: Check module contents
                    if (module.app) {
                        addResult('success', 'Test 2 - SUCCESS', 'Vue app instance found in exports');
                        console.log('Vue app instance:', module.app);
                    } else {
                        addResult('error', 'Test 2 - FAILED', 'Vue app instance not found in exports');
                    }
                    
                    if (module.pinia) {
                        addResult('success', 'Test 3 - SUCCESS', 'Pinia store found in exports');
                        console.log('Pinia store:', module.pinia);
                    } else {
                        addResult('error', 'Test 3 - FAILED', 'Pinia store not found in exports');
                    }
                    
                    // Test 3: Check if Vue app is mounted
                    setTimeout(() => {
                        const appElement = document.querySelector('#app');
                        if (appElement) {
                            addResult('info', 'Test 4', 'App mount point exists in DOM');
                            
                            // Check if Vue has mounted
                            const hasVueContent = appElement.innerHTML.includes('vue') || 
                                                appElement.innerHTML.includes('echarts') ||
                                                appElement.querySelector('[data-v-]') !== null;
                            
                            if (hasVueContent) {
                                addResult('success', 'Test 4 - SUCCESS', 'Vue.js appears to be mounted (Vue-specific content detected)');
                            } else {
                                addResult('info', 'Test 4 - INFO', 'Vue.js mount status unclear (no Vue-specific content detected yet)');
                            }
                        } else {
                            addResult('error', 'Test 4 - FAILED', 'App mount point (#app) not found in DOM');
                        }
                        
                        addResult('info', 'Test Complete', 'All tests finished. Check console for detailed logs.');
                    }, 2000);
                })
                .catch(error => {
                    addResult('error', 'Test 1 - FAILED', 'Failed to import Vue.js module: ' + error.message, error.stack);
                    console.error('Module import error:', error);
                });
                
        } catch (error) {
            addResult('error', 'Test Setup Failed', 'Error during test setup: ' + error.message, error.stack);
            console.error('Test setup error:', error);
        }
    </script>
</body>
</html>
