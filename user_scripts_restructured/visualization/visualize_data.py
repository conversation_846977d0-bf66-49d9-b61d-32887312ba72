#!/usr/bin/env python3
"""
Vue-ECharts Visualization Server for Nautilus Trader.

This script launches the Vue.js + ECharts visualization server with clean architecture.
Features enterprise-grade performance monitoring, bidirectional infinite scroll, 
and real-time WebSocket streaming. The lightweight chart implementation has been 
completely replaced with superior Vue-ECharts technology.

Usage:
    python scripts/visualize_data.py --port 8082 --catalog /path/to/catalog
    python scripts/visualize_data.py --websocket --port 8082 --debug
    python scripts/visualize_data.py --test-data --symbols "MNQ,ES,NQ"
"""

import sys
import os
import argparse
import logging
import subprocess
import time
from pathlib import Path
from datetime import datetime

# Using full import paths from PYTHONPATH

try:
    from user_scripts_restructured.core.config import ConfigManager
    from user_scripts_restructured.utils.logging_utils import setup_logging
    from user_scripts_restructured.visualization.vue_echarts.config import ChartConfig
    from user_scripts_restructured.visualization.vue_echarts.app import ChartServer
    from user_scripts_restructured.visualization.vue_echarts.websocket_app import WebSocketChartServer
    from user_scripts_restructured.visualization.vue_echarts.cli import (
        test_connection, list_instruments, perform_health_check
    )
except ImportError as e:
    print(f"Import error: {e}")
    print("Please ensure you're running from the correct directory and that vue_echarts module is available")
    print("The lightweight_chart implementation has been removed - only Vue-ECharts is supported")
    sys.exit(1)

logger = logging.getLogger(__name__)


def find_existing_visualization_processes() -> list:
    """Find existing visualization script processes."""
    try:
        # Use ps aux to find visualization processes
        result = subprocess.run(
            ['ps', 'aux'],
            capture_output=True,
            text=True,
            timeout=5
        )
        
        pids = []
        if result.returncode == 0:
            for line in result.stdout.split('\n'):
                # Look for visualization scripts, but exclude the current process
                if 'visualize_data.py' in line:
                    # Don't kill ourselves
                    if 'grep' not in line:
                        parts = line.split()
                        if len(parts) >= 2:
                            try:
                                pid = int(parts[1])
                                # Don't include our own PID
                                if pid != os.getpid():
                                    pids.append(pid)
                            except ValueError:
                                continue
        
        return pids
        
    except (subprocess.TimeoutExpired, subprocess.CalledProcessError, FileNotFoundError):
        logger.warning("Could not check for existing visualization processes")
        return []


def cleanup_existing_visualization_instances() -> bool:
    """Kill existing visualization processes and start fresh."""
    pids = find_existing_visualization_processes()
    
    if not pids:
        logger.info("No existing visualization processes found")
        return True
    
    logger.info(f"Found {len(pids)} existing visualization process(es): {pids}")
    
    killed_count = 0
    for pid in pids:
        try:
            # First try graceful termination
            logger.info(f"Terminating visualization process {pid}...")
            subprocess.run(['kill', '-TERM', str(pid)], timeout=2)
            
            # Wait briefly for graceful shutdown
            time.sleep(0.5)
            
            # Check if process is still running
            try:
                subprocess.run(['kill', '-0', str(pid)], timeout=1, check=True)
                # Process still running, force kill
                logger.info(f"Force killing stubborn process {pid}...")
                subprocess.run(['kill', '-KILL', str(pid)], timeout=2)
                killed_count += 1
            except subprocess.CalledProcessError:
                # Process not running anymore (kill -0 failed)
                killed_count += 1
                logger.info(f"Process {pid} terminated successfully")
                
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError) as e:
            logger.warning(f"Failed to kill process {pid}: {e}")
    
    # Give a moment for processes to fully terminate
    time.sleep(1)
    
    # Verify cleanup was successful
    remaining_pids = find_existing_visualization_processes()
    if remaining_pids:
        logger.warning(f"Some visualization processes still running: {remaining_pids}")
        return False
    
    logger.info(f"Successfully cleaned up {killed_count} visualization process(es)")
    return True


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Launch Vue-ECharts visualization server with clean architecture",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Launch Vue-ECharts server with real catalog data (recommended port 8082)
  python scripts/visualize_data.py --port 8082 --catalog /home/<USER>/nautilus_trader_fork/catalog

  # Launch with debug mode and WebSocket support (recommended)
  python scripts/visualize_data.py --websocket --port 8082 --debug --catalog /path/to/catalog

  # Launch with test data for development
  python scripts/visualize_data.py --test-data --symbols "MNQ,ES,NQ" --port 8082

  # Production deployment with optimized Vue-ECharts settings
  python scripts/visualize_data.py --port 8082 --catalog /path/to/catalog --log-level INFO

  # Health check and validation
  python scripts/visualize_data.py --health-check --catalog /path/to/catalog

  # List available instruments from catalog
  python scripts/visualize_data.py --list-instruments --catalog /path/to/catalog
        """
    )
    
    
    # Server configuration
    server_group = parser.add_argument_group('Server Configuration')
    server_group.add_argument(
        '--host',
        type=str,
        default='0.0.0.0',
        help='Host to bind to (default: 0.0.0.0)'
    )
    server_group.add_argument(
        '--port',
        type=int,
        default=8082,
        help='Port to listen on (default: 8082 - Vue-ECharts optimized, matches clean architecture)'
    )
    server_group.add_argument(
        '--websocket',
        action='store_true',
        default=True,
        help='Enable WebSocket support for real-time updates (default: enabled)'
    )
    server_group.add_argument(
        '--websocket-port',
        type=int,
        default=8082,
        help='Port for WebSocket server (default: same as main port - unified Vue-ECharts approach)'
    )
    
    # Data configuration
    data_group = parser.add_argument_group('Data Configuration')
    data_group.add_argument(
        '--catalog',
        type=str,
        default='/home/<USER>/nautilus_trader_fork/catalog',
        help='Path to the data catalog (default: /home/<USER>/nautilus_trader_fork/catalog)'
    )
    data_group.add_argument(
        '--max-points',
    type=int,
        default=50000,
        help='Maximum number of data points to display (default: 50000 - Vue-ECharts optimized)'
    )
    data_group.add_argument(
        '--cache-expiry',
        type=int,
        default=1800,
        help='Cache expiry time in seconds (default: 1800)'
    )
    
    # Test data
    test_group = parser.add_argument_group('Test Data')
    test_group.add_argument(
        '--test-data',
        action='store_true',
        help='Generate test data if catalog is empty'
    )
    test_group.add_argument(
        '--symbols',
        type=str,
        default='MNQ,ES,NQ',
        help='Symbols for test data (default: MNQ,ES,NQ)'
    )
    test_group.add_argument(
        '--bars',
        type=int,
        default=1000,
        help='Number of bars for test data (default: 1000)'
    )
    
    # Configuration
    config_group = parser.add_argument_group('Configuration')
    config_group.add_argument(
        '--config',
        type=str,
        help='Path to configuration file'
    )
    config_group.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='Logging level (default: INFO). Use DEBUG for development with Vue-ECharts debugging.'
    )
    
    # Utility commands
    utility_group = parser.add_argument_group('Utility Commands')
    utility_group.add_argument(
        '--test-connection',
        action='store_true',
        help='Test data source connection and exit'
    )
    utility_group.add_argument(
        '--list-instruments',
        action='store_true',
        help='List available instruments and exit'
    )
    utility_group.add_argument(
        '--health-check',
        action='store_true',
        help='Perform health check and exit'
    )
    utility_group.add_argument(
        '--validate-config',
        action='store_true',
        help='Validate configuration and exit'
    )
    
    return parser.parse_args()


def create_chart_config(args, main_config):
    """Create chart configuration from arguments and main config."""
    # Start with environment variables and args (includes main config fallback)
    config = ChartConfig.from_env_and_args(args)
    
    # Override with command line arguments
    if args.host:
        config.host = args.host
    if args.port:
        config.port = args.port
    if args.catalog:
        config.catalog_path = args.catalog
    if args.max_points:
        config.max_points = args.max_points
    if args.cache_expiry:
        config.cache_expiry = args.cache_expiry
    if args.log_level:
        config.log_level = args.log_level
        # Auto-enable Flask debug mode when log level is DEBUG
        config.debug = (args.log_level == 'DEBUG')
    if args.websocket:
        config.enable_websocket = args.websocket
        # WebSocket runs on same port as HTTP server in unified approach
        config.websocket_port = config.port
    if args.test_data:
        config.enable_test_data = args.test_data
        config.test_symbols = args.symbols
        config.test_bars = args.bars
    # Vue-ECharts is the only implementation - always enabled
    config.use_vue_echarts = True
    config.use_typescript = True  # Vue-ECharts requires TypeScript
    
    return config


def validate_args(args) -> bool:
    """Validate command line arguments."""
    errors = []
    
    # Validate port ranges
    if not (1024 <= args.port <= 65535):
        errors.append(f"Invalid port: {args.port}")
    
    if args.websocket and not (1024 <= args.websocket_port <= 65535):
        errors.append(f"Invalid WebSocket port: {args.websocket_port}")
    
    # Validate paths
    if args.catalog and not Path(args.catalog).exists():
        if not args.test_data:
            errors.append(f"Catalog path does not exist: {args.catalog}")
    
    if args.config and not Path(args.config).exists():
        errors.append(f"Config file does not exist: {args.config}")
    
    # Validate numeric values
    if args.max_points <= 0:
        errors.append(f"Invalid max-points: {args.max_points}")
    
    if args.cache_expiry <= 0:
        errors.append(f"Invalid cache-expiry: {args.cache_expiry}")
    
    if args.bars <= 0:
        errors.append(f"Invalid bars: {args.bars}")
    
    if errors:
        for error in errors:
            logger.error(error)
        return False
    
    return True


def launch_vue_echarts_server(config) -> int:
    """Launch the Vue-ECharts server with advanced features."""
    try:
        logger.info("🚀 Launching Vue.js + ECharts Visualization Server...")
        logger.info(f"📊 Server URL: {config.get_server_url()}")
        logger.info(f"📁 Catalog: {config.catalog_path}")
        logger.info(f"🔌 WebSocket: {'Enabled' if config.enable_websocket else 'Disabled'}")
        logger.info(f"⚡ Vue-ECharts: Advanced infinite loading + performance monitoring")
        
        if config.enable_websocket:
            logger.info(f"🔌 WebSocket URL: {config.get_websocket_url()}")
            logger.info("🎯 Vue-ECharts Features: Bidirectional infinite scroll, real-time updates")
            logger.info("📊 Performance Monitoring: Health scoring, memory tracking, FPS monitoring")
            server = WebSocketChartServer(config)
        else:
            logger.warning("⚠️  WebSocket disabled - Vue-ECharts features limited")
            server = ChartServer(config)
        
        # Display Vue-ECharts access URLs
        logger.info("🚀 Vue-ECharts Server Ready!")
        logger.info("=" * 60)
        logger.info("📊 Vue-ECharts Chart URLs:")
        logger.info(f"   Main Interface:     {config.get_server_url()}/websocket")
        logger.info(f"   MNQ Chart:          {config.get_server_url()}/chart/MNQ.CME")
        logger.info(f"   Vue-ECharts Route:  {config.get_server_url()}/vue/chart/MNQ.CME")
        logger.info("📡 API Endpoints:")
        logger.info(f"   Chart Data:         {config.get_server_url()}/api/chart-data/MNQ.CME")
        logger.info(f"   Instruments:        {config.get_server_url()}/api/instruments")
        logger.info("=" * 60)
        logger.info("✨ Vue-ECharts Features Available:")
        logger.info("   🔄 Bidirectional infinite scroll")
        logger.info("   📊 Real-time performance monitoring")  
        logger.info("   🎯 Enterprise health scoring")
        logger.info("   ⚡ Sub-10ms response times")
        logger.info("   🔌 WebSocket real-time streaming")
        logger.info("=" * 60)
        
        # Start the server (this will block)
        server.start_server()
        
        return 0
        
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
        return 0
    except Exception as e:
        logger.error(f"Error starting server: {e}")
        return 1


def main():
    """Main entry point."""
    start_time = datetime.now()
    
    try:
        # Parse arguments
        args = parse_args()
        
        # Set up logging
        setup_logging(
            level=args.log_level,
            console=True  # Enable console logging for visualization scripts
        )
        
        logger.info("🚀 Starting Nautilus Trader Vue-ECharts Visualization")
        logger.info("📊 Visualization type: Vue.js + ECharts (Advanced)")
        logger.info("✨ Features: Infinite scroll, real-time monitoring, enterprise performance")
        
        # Validate arguments
        if not validate_args(args):
            return 1
        
        # Load main configuration (graceful fallback for Vue-ECharts)
        try:
            main_config = ConfigManager.load_config(args.config) if args.config else None
            if main_config:
                logger.info("✅ Loaded main configuration")
        except Exception as e:
            logger.warning(f"⚠️  Could not load main config: {e}")
            main_config = None
            logger.info("🔧 Using Vue-ECharts default configuration")
        
        # Create visualization-specific configuration for Vue-ECharts
        config = create_chart_config(args, main_config)
        
        # Validate configuration
        if not config.validate():
            logger.error("Configuration validation failed")
            return 1
        
        # Clean up existing visualization instances (before utility commands)
        if not args.test_connection and not args.list_instruments and not args.health_check and not args.validate_config:
            if not cleanup_existing_visualization_instances():
                logger.error("Failed to clean up existing visualization processes")
                return 1
        
        # Handle utility commands
        if args.validate_config:
            logger.info("✓ Configuration is valid")
            return 0
        
        if args.test_connection:
            return 0 if test_connection(config) else 1
        
        if args.list_instruments:
            return 0 if list_instruments(config) else 1
        
        if args.health_check:
            return 0 if perform_health_check(config) else 1
        
        # Launch the Vue-ECharts server (single implementation)
        return launch_vue_echarts_server(config)
        
    except KeyboardInterrupt:
        logger.info("Visualization stopped by user")
        return 0
    except Exception as e:
        logger.error(f"Error: {e}")
        if args.log_level == 'DEBUG':
            import traceback
            logger.debug(traceback.format_exc())
        return 1
    finally:
        duration = (datetime.now() - start_time).total_seconds()
        logger.info(f"Visualization session duration: {duration:.1f} seconds")


if __name__ == "__main__":
    sys.exit(main())
