#!/usr/bin/env python3
"""
Debug script to test Vue.js chart loading
"""

import requests
import json
import time
from pathlib import Path

def test_endpoints():
    """Test all API endpoints"""
    base_url = "http://localhost:8082"
    
    print("🔍 Testing API Endpoints...")
    
    # Test 1: Instruments
    try:
        response = requests.get(f"{base_url}/api/instruments", timeout=5)
        if response.status_code == 200:
            instruments = response.json()
            print(f"✅ Instruments API: {len(instruments)} instruments found")
            for inst in instruments:
                print(f"   - {inst.get('id', 'Unknown')}")
        else:
            print(f"❌ Instruments API failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Instruments API error: {e}")
    
    # Test 2: Chart Data
    try:
        response = requests.get(f"{base_url}/api/chart-data/MNQ.CME?limit=5", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Chart Data API: {data.get('bars_returned', 0)} bars returned")
            print(f"   Quality Score: {data.get('data_quality', {}).get('metrics', {}).get('quality_score', 'N/A')}")
        else:
            print(f"❌ Chart Data API failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Chart Data API error: {e}")
    
    # Test 3: Static JS Bundle
    try:
        response = requests.head(f"{base_url}/static/js/vue-echarts-app.js", timeout=5)
        if response.status_code == 200:
            size = int(response.headers.get('content-length', 0))
            content_type = response.headers.get('content-type', 'Unknown')
            print(f"✅ JS Bundle: {size:,} bytes, Content-Type: {content_type}")
        else:
            print(f"❌ JS Bundle failed: {response.status_code}")
    except Exception as e:
        print(f"❌ JS Bundle error: {e}")
    
    # Test 4: Chart Page
    try:
        response = requests.get(f"{base_url}/chart/MNQ.CME", timeout=5)
        if response.status_code == 200:
            html = response.text
            has_vue_script = 'vue-echarts-app.js' in html
            has_app_div = 'id="app"' in html
            print(f"✅ Chart Page: HTML served ({len(html):,} chars)")
            print(f"   Vue script included: {has_vue_script}")
            print(f"   App div present: {has_app_div}")
        else:
            print(f"❌ Chart Page failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Chart Page error: {e}")

def check_build_status():
    """Check if the build files are up to date"""
    print("\n🔧 Checking Build Status...")
    
    # Check if JS bundle exists and is recent
    js_bundle = Path("vue_echarts/static/js/vue-echarts-app.js")
    if js_bundle.exists():
        size = js_bundle.stat().st_size
        mtime = js_bundle.stat().st_mtime
        age_minutes = (time.time() - mtime) / 60
        print(f"✅ JS Bundle exists: {size:,} bytes, {age_minutes:.1f} minutes old")
        
        # Check if it has exports
        try:
            with open(js_bundle, 'r') as f:
                content = f.read()
                has_exports = 'export{' in content or 'export {' in content
                has_vue = 'Vue' in content or 'vue' in content
                has_echarts = 'echarts' in content or 'ECharts' in content
                print(f"   Has exports: {has_exports}")
                print(f"   Contains Vue: {has_vue}")
                print(f"   Contains ECharts: {has_echarts}")
        except Exception as e:
            print(f"   ❌ Error reading bundle: {e}")
    else:
        print("❌ JS Bundle not found")
    
    # Check source files
    src_files = [
        "src/vue-echarts-app.ts",
        "src/components/EChartsApp.vue",
        "src/components/VueEChartsChart.vue"
    ]
    
    print("\n📁 Source Files:")
    for src_file in src_files:
        path = Path(src_file)
        if path.exists():
            size = path.stat().st_size
            print(f"   ✅ {src_file}: {size:,} bytes")
        else:
            print(f"   ❌ {src_file}: Missing")

def main():
    print("🚀 Vue.js Chart Debug Tool")
    print("=" * 50)
    
    test_endpoints()
    check_build_status()
    
    print("\n" + "=" * 50)
    print("🎯 Summary:")
    print("If all tests pass, the issue is likely in browser JavaScript execution.")
    print("Check browser console at: http://localhost:8082/chart/MNQ.CME")
    print("Or test module loading at: http://localhost:8082/static/test_vue_loading.html")

if __name__ == "__main__":
    main()
