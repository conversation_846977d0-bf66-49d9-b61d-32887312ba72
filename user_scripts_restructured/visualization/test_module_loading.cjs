#!/usr/bin/env node

/**
 * Test script to verify Vue.js module loading using Node.js
 * This simulates the browser ES module import to check if exports are working
 */

const fs = require('fs');
const path = require('path');

async function testModuleExports() {
    console.log('🧪 Testing Vue.js Module Exports...');
    console.log('=' * 50);
    
    const bundlePath = path.join(__dirname, 'vue_echarts/static/js/vue-echarts-app.js');
    
    // Test 1: Check if file exists
    if (!fs.existsSync(bundlePath)) {
        console.error('❌ Bundle file not found:', bundlePath);
        return false;
    }
    
    console.log('✅ Bundle file exists');
    
    // Test 2: Check file size
    const stats = fs.statSync(bundlePath);
    const sizeKB = Math.round(stats.size / 1024);
    console.log(`✅ Bundle size: ${sizeKB} KB`);
    
    // Test 3: Check for exports at the end
    const content = fs.readFileSync(bundlePath, 'utf8');
    const lastLines = content.split('\n').slice(-10).join('\n');
    
    console.log('\n📄 Last 10 lines of bundle:');
    console.log('---');
    console.log(lastLines);
    console.log('---');
    
    // Test 4: Check for export statement
    const hasExports = content.includes('export {') && 
                      (content.includes('as app') || content.includes('app,')) &&
                      (content.includes('as pinia') || content.includes('pinia'));
    
    if (hasExports) {
        console.log('✅ ES module exports found');
        
        // Extract the export statement
        const exportMatch = content.match(/export\s*\{[^}]+\}/);
        if (exportMatch) {
            console.log('📦 Export statement:', exportMatch[0]);
        }
    } else {
        console.log('❌ ES module exports NOT found');
        return false;
    }
    
    // Test 5: Check for Vue.js and ECharts content
    const hasVue = content.includes('Vue') || content.includes('createApp');
    const hasECharts = content.includes('echarts') || content.includes('ECharts');
    const hasPinia = content.includes('pinia') || content.includes('Pinia');
    
    console.log(`✅ Contains Vue.js: ${hasVue}`);
    console.log(`✅ Contains ECharts: ${hasECharts}`);
    console.log(`✅ Contains Pinia: ${hasPinia}`);
    
    // Test 6: Check for mount code
    const hasMount = content.includes('mount(') && content.includes('#app');
    console.log(`✅ Contains mount code: ${hasMount}`);
    
    console.log('\n🎯 Module Export Test Results:');
    console.log(`   File exists: ✅`);
    console.log(`   Proper size: ✅ (${sizeKB} KB)`);
    console.log(`   Has exports: ${hasExports ? '✅' : '❌'}`);
    console.log(`   Has Vue.js: ${hasVue ? '✅' : '❌'}`);
    console.log(`   Has ECharts: ${hasECharts ? '✅' : '❌'}`);
    console.log(`   Has Pinia: ${hasPinia ? '✅' : '❌'}`);
    console.log(`   Has mount: ${hasMount ? '✅' : '❌'}`);
    
    const allTestsPassed = hasExports && hasVue && hasECharts && hasPinia && hasMount;
    
    console.log('\n' + '=' * 50);
    if (allTestsPassed) {
        console.log('🎉 ALL TESTS PASSED! Vue.js module should load correctly in browser.');
        console.log('🌐 Ready to test at: http://localhost:8082/chart/MNQ.CME');
    } else {
        console.log('❌ Some tests failed. Module may not load correctly.');
    }
    
    return allTestsPassed;
}

// Run the test
testModuleExports().catch(console.error);
