"""
Vue-ECharts visualization module for Nautilus Trader.

This module provides Flask application for serving interactive
financial charts using Vue.js + ECharts components.
"""

import os
import time
import logging
import json
from datetime import datetime
from typing import Optional, Dict, Any

try:
    from flask import Flask, render_template, jsonify, request, redirect, url_for
    FLASK_AVAILABLE = True
except ImportError:
    FLASK_AVAILABLE = False
    Flask = None

try:
    import numpy as np
    import pandas as pd
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False
    np = None
    pd = None

try:
    from .config import ChartConfig, ChartConfigManager
    from .data_loader import load_instrument_data, load_instrument_data_optimized, get_available_instruments, set_catalog_path, clear_arrow_cache, load_chart_data_with_direct_pyarrow_pipeline
    from .utils.chart_utils import create_chart_response
    from .utils.test_data import create_test_catalog
except ImportError:
    # For standalone testing
    try:
        from config import ChartConfig, ChartConfigManager
        from data_loader import load_instrument_data, load_instrument_data_optimized, get_available_instruments, set_catalog_path, clear_arrow_cache, load_chart_data_with_direct_pyarrow_pipeline
        from utils.chart_utils import create_chart_response
        from utils.test_data import create_test_catalog
    except ImportError:
        # Fallback for missing utils
        def create_chart_response(*args, **kwargs):
            return {'error': 'Chart utils not available'}
        def create_test_catalog(*args, **kwargs):
            return None

        # Create minimal config classes if not available
        class ChartConfig:
            def __init__(self, **kwargs):
                self.host = kwargs.get('host', '0.0.0.0')
                self.port = kwargs.get('port', 8080)
                self.catalog_path = kwargs.get('catalog_path', '/tmp/test_catalog')
                self.debug = kwargs.get('debug', False)
                self.max_points = kwargs.get('max_points', 1000)
                self.enable_test_data = kwargs.get('enable_test_data', False)
                self.test_symbols = kwargs.get('test_symbols', 'MNQCONT.CME,MNQ.CME,TEST')
                self.test_bars = kwargs.get('test_bars', 1000)

            def validate(self):
                return True

            def get_server_url(self):
                return f"http://{self.host}:{self.port}"

        class ChartConfigManager:
            def __init__(self):
                pass

logger = logging.getLogger(__name__)


class NumpyEncoder(json.JSONEncoder):
    """Custom JSON encoder to handle numpy and pandas types."""
    def default(self, obj):
        if NUMPY_AVAILABLE:
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, (pd.Timestamp, pd.DatetimeIndex)):
                return obj.isoformat() if hasattr(obj, 'isoformat') else str(obj)
        return super().default(obj)


def safe_jsonify(data, status_code=200):
    """Custom jsonify that handles numpy types."""
    try:
        json_str = json.dumps(data, cls=NumpyEncoder)
        response = Flask.response_class(
            json_str,
            mimetype='application/json'
        )
        response.status_code = status_code
        return response
    except Exception as e:
        logger.error(f"Error in safe_jsonify: {e}")
        # Fallback to regular jsonify with error
        return jsonify({'error': 'JSON serialization error'}), 500


class ChartServer:
    """
    Vue-ECharts server implementation.

    This class provides a Flask-based web server for visualizing financial data
    using Vue.js + ECharts components.
    """

    def __init__(self, config: ChartConfig):
        """
        Initialize the chart server.

        Args:
            config: Chart configuration
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self.app = None

        if not FLASK_AVAILABLE:
            raise ImportError("Flask is required for ChartServer. Install with: pip install Flask")

        # Set the catalog path using the functional approach
        set_catalog_path(config.catalog_path)

        self._setup_flask_app()

    def _setup_flask_app(self):
        """Set up the Flask application with routes."""
        import os
        from pathlib import Path
        
        # Get absolute path to static folder
        static_path = Path(__file__).parent / 'static'
        
        self.app = Flask(
            __name__,
            template_folder='templates',
            static_folder=str(static_path.absolute())
        )

        # Configure Flask
        self.app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', os.urandom(32).hex())
        
        # Add security headers including CSP to allow necessary JavaScript execution
        @self.app.after_request
        def add_security_headers(response):
            # Content Security Policy for Vue-ECharts components
            # Note: 'unsafe-eval' and 'unsafe-inline' are required for:
            # - ECharts: Dynamic chart rendering and data processing
            # - Vue.js: Component compilation and reactive updates
            # - Socket.IO: WebSocket real-time functionality
            # These directives are necessary for the visualization to function properly
            #
            # External CDN Dependencies (Security Note):
            # - unpkg.com: Main CDN for Vue.js and ECharts libraries
            # - cdn.jsdelivr.net: Backup CDN for library assets
            # - cdnjs.cloudflare.com: Additional CDN for common libraries
            # SECURITY CONSIDERATION: For production deployments, consider:
            # 1. Self-hosting all JavaScript libraries to eliminate supply chain risks
            # 2. Using Subresource Integrity (SRI) hashes for CDN resources
            # 3. Implementing Content Security Policy reporting for monitoring
            csp_policy = (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-eval' 'unsafe-inline' "
                "https://unpkg.com https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; "
                "style-src 'self' 'unsafe-inline' "
                "https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; "
                "connect-src 'self' ws: wss: http: https:; "
                "img-src 'self' data: https:; "
                "font-src 'self' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; "
                "object-src 'none'; "
                "base-uri 'self'"
            )
            response.headers['Content-Security-Policy'] = csp_policy
            
            # Additional security headers
            response.headers['X-Content-Type-Options'] = 'nosniff'
            response.headers['X-Frame-Options'] = 'DENY'
            response.headers['X-XSS-Protection'] = '1; mode=block'
            response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
            
            return response
        
        # Set custom JSON encoder to handle numpy types
        if hasattr(self.app, 'json_encoder'):
            self.app.json_encoder = NumpyEncoder
        elif hasattr(self.app, 'json'):
            self.app.json.encoder = NumpyEncoder

        # Register routes
        self._register_routes()

        # Set up logging
        if not self.config.debug:
            log = logging.getLogger('werkzeug')
            log.setLevel(logging.WARNING)

    def _register_routes(self):
        """Register Flask routes."""

        @self.app.route('/')
        def index():
            """Render the TypeScript index page (unified approach - redirected from legacy)."""
            try:
                instruments = get_available_instruments()
                return render_template('index_ts.html', instruments=instruments)
            except Exception as e:
                logger.error(f"Error loading TypeScript index page: {e}")
                return render_template('index_ts.html', instruments=[])

        @self.app.route('/chart/<instrument_id>')
        def unified_chart(instrument_id: str):
            """Render the Vue-ECharts chart (superior to TradingView)."""
            debug = request.args.get('debug', 'false').lower() == 'true'
            return render_template('vue_echarts_chart.html', 
                                   instrument_id=instrument_id, 
                                   debug=debug)

        @self.app.route('/api/instruments')
        def get_instruments():
            """Return available instruments as JSON."""
            try:
                instruments = get_available_instruments()
                return safe_jsonify(instruments)
            except Exception as e:
                logger.error(f"Error getting instruments: {e}")
                from .validation import create_error_response
                return safe_jsonify(create_error_response(
                    "Failed to retrieve instruments list",
                    "server",
                    "INTERNAL_SERVER_ERROR"
                ), 500)

        @self.app.route('/api/chart-data/<instrument_id>')
        def get_chart_data(instrument_id: str):
            """Return chart data for a specific instrument."""
            try:
                # Import validation and new pipeline at function level
                from .validation import validate_chart_data_request
                
                # Prepare request args for validation
                request_args = {
                    'instrument_id': instrument_id,
                    'timeframe': request.args.get('timeframe', '1min'),
                    'limit': request.args.get('limit', str(self.config.max_points)),
                    'before_timestamp_seconds': request.args.get('before_timestamp_seconds'),
                    'after_timestamp_seconds': request.args.get('after_timestamp_seconds')
                }
                
                # Phase 2.4: Extract new pipeline parameters
                pipeline_mode = request.args.get('pipeline', 'legacy')  # 'direct_pyarrow' or 'legacy'
                format_type = request.args.get('format', 'default')     # 'echarts' or 'default'
                
                # Validate request parameters
                is_valid, error_response, validated_params = validate_chart_data_request(request_args)
                if not is_valid:
                    logger.warning(f"Validation error for chart data request: {error_response}")
                    return jsonify(error_response), 400
                
                # Extract validated parameters
                instrument_id = validated_params['instrument_id']
                timeframe = validated_params['timeframe']
                limit = validated_params['limit']
                before_datetime = validated_params.get('before_datetime')
                after_datetime = validated_params.get('after_datetime')

                # Phase 2.4: Use direct PyArrow pipeline when requested
                if pipeline_mode == 'direct_pyarrow':
                    try:
                        # Convert datetime objects to expected format for the new pipeline
                        before_dt = before_datetime.isoformat() if before_datetime else None
                        after_dt = after_datetime.isoformat() if after_datetime else None
                        
                        # Load data using direct PyArrow pipeline
                        response_data = load_chart_data_with_direct_pyarrow_pipeline(
                            instrument_dir=None,  # Will be resolved from catalog
                            instrument_id=instrument_id,
                            start_date=None,
                            end_date=None,
                            columns=None,
                            before_datetime=before_dt,
                            after_datetime=after_dt,
                            limit=limit,
                            format_type=format_type,
                            pipeline_mode='direct_pyarrow'
                        )
                        
                        # Return direct pipeline response
                        return safe_jsonify(response_data)
                        
                    except Exception as e:
                        logger.warning(f"Direct PyArrow pipeline failed for {instrument_id}: {e}")
                        # Fall back to legacy pipeline
                        logger.info(f"Falling back to legacy pipeline for {instrument_id}")
                
                # Phase 2.7: Smart cache fallback with automatic detection
                elif pipeline_mode == 'smart_cache_fallback':
                    try:
                        # Convert datetime objects to expected format
                        before_dt = before_datetime.isoformat() if before_datetime else None
                        after_dt = after_datetime.isoformat() if after_datetime else None
                        
                        # Load data using smart cache fallback pipeline
                        response_data = load_chart_data_with_direct_pyarrow_pipeline(
                            instrument_dir=None,  # Will be resolved from catalog
                            instrument_id=instrument_id,
                            start_date=None,
                            end_date=None,
                            columns=None,
                            before_datetime=before_dt,
                            after_datetime=after_dt,
                            limit=limit,
                            format_type=format_type,
                            pipeline_mode='smart_cache_fallback'
                        )
                        
                        # Return smart cache response
                        return safe_jsonify(response_data)
                        
                    except Exception as e:
                        logger.warning(f"Smart cache fallback pipeline failed for {instrument_id}: {e}")
                        # Fall back to legacy pipeline
                        logger.info(f"Falling back to legacy pipeline for {instrument_id}")

                # Legacy pipeline (default behavior)
                df = load_instrument_data_optimized(
                    instrument_id=instrument_id,
                    timeframe=timeframe,
                    limit=limit,
                    before_datetime=before_datetime
                )

                if df.empty:
                    message = ('No more historical data found' if before_datetime
                             else f'No data found for instrument {instrument_id} with timeframe {timeframe}')
                    status_code = 200 if before_datetime else 404

                    return safe_jsonify({
                        'ohlc': [],
                        'volume': [],
                        'instrument': instrument_id,
                        'timeframe': timeframe,
                        'message': message
                    }, status_code)

                # Create chart response
                response = create_chart_response(
                    df=df,
                    instrument_id=instrument_id,
                    timeframe=timeframe,
                    include_moving_averages=False
                )

                return safe_jsonify(response)

            except Exception as e:
                logger.error(f"Error getting chart data for {instrument_id}: {e}")
                from .validation import create_error_response
                return safe_jsonify(create_error_response(
                    f"Failed to retrieve chart data for {instrument_id}",
                    "server",
                    "INTERNAL_SERVER_ERROR"
                ), 500)

        @self.app.route('/api/health')
        def health_check():
            """Health check endpoint."""
            try:
                health_status = self._health_check()
                return jsonify(health_status)
            except Exception as e:
                logger.error(f"Health check failed: {e}")
                from .validation import create_error_response
                return jsonify(create_error_response(
                    "Health check failed",
                    "server",
                    "HEALTH_CHECK_FAILED"
                )), 500

        @self.app.route('/api/validation-info')
        def validation_info():
            """Get API validation rules and constraints."""
            try:
                from .validation import get_validation_info
                return jsonify(get_validation_info())
            except Exception as e:
                logger.error(f"Error getting validation info: {e}")
                from .validation import create_error_response
                return jsonify(create_error_response(
                    "Failed to retrieve validation information",
                    "server",
                    "INTERNAL_SERVER_ERROR"
                )), 500

        @self.app.route('/api/instruments/<instrument_id>/date-range')
        def get_instrument_date_range(instrument_id: str):
            """Get date range of available data for an instrument."""
            try:
                # Import validation and discovery at function level
                from .validation import validate_instrument_id, create_error_response
                from .data_discovery import get_instrument_date_range as get_date_range
                
                # Validate instrument ID
                is_valid, error_msg = validate_instrument_id(instrument_id)
                if not is_valid:
                    return jsonify(create_error_response(error_msg, "instrument_id", "INVALID_INSTRUMENT_ID")), 400
                
                # Get date range
                date_range = get_date_range(instrument_id)
                if date_range is None:
                    return jsonify(create_error_response(
                        f"No data found for instrument {instrument_id}",
                        "instrument_id",
                        "INSTRUMENT_NOT_FOUND"
                    )), 404
                
                return jsonify(date_range)
                
            except Exception as e:
                logger.error(f"Error getting date range for {instrument_id}: {e}")
                from .validation import create_error_response
                return jsonify(create_error_response(
                    f"Failed to retrieve date range for {instrument_id}",
                    "server",
                    "INTERNAL_SERVER_ERROR"
                )), 500

        @self.app.route('/api/instruments/<instrument_id>/summary')
        def get_instrument_summary(instrument_id: str):
            """Get comprehensive summary for an instrument."""
            try:
                # Import validation and discovery at function level
                from .validation import validate_instrument_id, create_error_response
                from .data_discovery import get_instrument_summary as get_summary
                
                # Validate instrument ID
                is_valid, error_msg = validate_instrument_id(instrument_id)
                if not is_valid:
                    return jsonify(create_error_response(error_msg, "instrument_id", "INVALID_INSTRUMENT_ID")), 400
                
                # Get summary
                summary = get_summary(instrument_id)
                if summary is None:
                    return jsonify(create_error_response(
                        f"No data found for instrument {instrument_id}",
                        "instrument_id", 
                        "INSTRUMENT_NOT_FOUND"
                    )), 404
                
                return jsonify(summary)
                
            except Exception as e:
                logger.error(f"Error getting summary for {instrument_id}: {e}")
                from .validation import create_error_response
                return jsonify(create_error_response(
                    f"Failed to retrieve summary for {instrument_id}",
                    "server",
                    "INTERNAL_SERVER_ERROR"
                )), 500

        @self.app.route('/api/instruments/<instrument_id>/timeframes')
        def get_instrument_timeframes(instrument_id: str):
            """Get available timeframes for an instrument."""
            try:
                # Import validation and discovery at function level
                from .validation import validate_instrument_id, create_error_response
                from .data_discovery import get_instrument_timeframes as get_timeframes, check_instrument_exists
                
                # Validate instrument ID
                is_valid, error_msg = validate_instrument_id(instrument_id)
                if not is_valid:
                    return jsonify(create_error_response(error_msg, "instrument_id", "INVALID_INSTRUMENT_ID")), 400
                
                # Check if instrument exists
                if not check_instrument_exists(instrument_id):
                    return jsonify(create_error_response(
                        f"Instrument {instrument_id} not found",
                        "instrument_id",
                        "INSTRUMENT_NOT_FOUND"
                    )), 404
                
                # Get timeframes
                timeframes = get_timeframes(instrument_id)
                
                return jsonify({
                    'instrument_id': instrument_id,
                    'available_timeframes': timeframes,
                    'total_timeframes': len(timeframes)
                })
                
            except Exception as e:
                logger.error(f"Error getting timeframes for {instrument_id}: {e}")
                from .validation import create_error_response
                return jsonify(create_error_response(
                    f"Failed to retrieve timeframes for {instrument_id}",
                    "server",
                    "INTERNAL_SERVER_ERROR"
                )), 500

        @self.app.route('/api/catalog/health')
        def get_catalog_health():
            """Get health information about the data catalog."""
            try:
                from .data_discovery import get_catalog_health
                return jsonify(get_catalog_health())
            except Exception as e:
                logger.error(f"Error getting catalog health: {e}")
                from .validation import create_error_response
                return jsonify(create_error_response(
                    "Failed to retrieve catalog health information",
                    "server",
                    "INTERNAL_SERVER_ERROR"
                )), 500

        @self.app.route('/api/cache/clear')
        def clear_cache():
            """Clear cache for testing and maintenance"""
            try:
                instrument_id = request.args.get('instrument_id')
                cleared_components = []
                
                # Clear enhanced cache if available
                if (hasattr(self.data_loader, 'enhanced_cache') and 
                    self.data_loader.enhanced_cache is not None):
                    self.data_loader.enhanced_cache.clear_cache()
                    cleared_components.append('enhanced_cache')
                
                # Always clear legacy cache
                if hasattr(self.data_loader, 'clear_cache'):
                    self.data_loader.clear_cache()
                    cleared_components.append('legacy_cache')
                
                # Clear arrow cache
                clear_arrow_cache(instrument_id)
                cleared_components.append('arrow_cache')
                
                message = (f"Cache cleared for {instrument_id}" if instrument_id
                          else "All caches cleared")
                
                return jsonify({
                    'status': 'cache_cleared',
                    'components_cleared': cleared_components,
                    'message': message,
                    'timestamp': time.time()
                })
            except Exception as e:
                logger.error(f"Error clearing cache: {e}")
                from .validation import create_error_response
                return jsonify(create_error_response(
                    "Failed to clear cache",
                    "server",
                    "CACHE_CLEAR_FAILED"
                )), 500

        @self.app.route('/api/cache/stats')
        def cache_stats():
            """Get comprehensive cache statistics with memory usage."""
            try:
                # Import cache modules and functions
                from .data_loader import DATA_CACHE, ARROW_DATASET_CACHE, CACHE_TIMESTAMPS, CACHE_EXPIRY, MAX_CACHE_ITEMS
                import sys
                import psutil
                import time
                from datetime import datetime, timezone
                
                # Calculate memory usage for each cache
                data_cache_memory = 0
                for key, df in DATA_CACHE.items():
                    try:
                        data_cache_memory += df.memory_usage(deep=True).sum()
                    except:
                        pass
                
                arrow_cache_memory = 0
                for key, dataset in ARROW_DATASET_CACHE.items():
                    try:
                        # Estimate memory usage for arrow datasets
                        arrow_cache_memory += sys.getsizeof(dataset)
                    except:
                        pass
                
                # Get system memory info
                process = psutil.Process()
                process_memory = process.memory_info()
                system_memory = psutil.virtual_memory()
                
                # Cache hit rate calculation (simplified)
                total_cache_items = len(DATA_CACHE) + len(ARROW_DATASET_CACHE)
                cache_hit_rate = min(100.0, (total_cache_items / max(MAX_CACHE_ITEMS, 1)) * 100)
                
                # Calculate cache age statistics
                current_time = time.time()
                cache_ages = []
                expired_count = 0
                
                for timestamp in CACHE_TIMESTAMPS.values():
                    age = current_time - timestamp
                    cache_ages.append(age)
                    if age > CACHE_EXPIRY:
                        expired_count += 1
                
                avg_cache_age = sum(cache_ages) / len(cache_ages) if cache_ages else 0
                
                stats = {
                    'cache_summary': {
                        'total_cache_items': total_cache_items,
                        'data_cache_count': len(DATA_CACHE),
                        'arrow_cache_count': len(ARROW_DATASET_CACHE),
                        'cache_timestamps_count': len(CACHE_TIMESTAMPS),
                        'cache_hit_rate_percent': round(cache_hit_rate, 2),
                        'expired_items_count': expired_count
                    },
                    'memory_usage': {
                        'data_cache_mb': round(data_cache_memory / (1024 * 1024), 2),
                        'arrow_cache_mb': round(arrow_cache_memory / (1024 * 1024), 2),
                        'total_cache_mb': round((data_cache_memory + arrow_cache_memory) / (1024 * 1024), 2),
                        'process_memory_mb': round(process_memory.rss / (1024 * 1024), 2),
                        'system_memory_total_gb': round(system_memory.total / (1024 * 1024 * 1024), 2),
                        'system_memory_available_gb': round(system_memory.available / (1024 * 1024 * 1024), 2),
                        'system_memory_used_percent': system_memory.percent
                    },
                    'cache_performance': {
                        'avg_cache_age_seconds': round(avg_cache_age, 2),
                        'cache_expiry_seconds': CACHE_EXPIRY,
                        'max_cache_items': MAX_CACHE_ITEMS,
                        'cache_utilization_percent': round((total_cache_items / max(MAX_CACHE_ITEMS, 1)) * 100, 2)
                    },
                    'cache_details': {
                        'data_cache_keys': list(DATA_CACHE.keys()),
                        'arrow_cache_keys': list(ARROW_DATASET_CACHE.keys())
                    },
                    'generated_at': datetime.now(timezone.utc).isoformat()
                }
                
                return jsonify(stats)
            except Exception as e:
                logger.error(f"Error getting cache stats: {e}")
                from .validation import create_error_response
                return jsonify(create_error_response(
                    "Failed to retrieve cache statistics",
                    "server",
                    "INTERNAL_SERVER_ERROR"
                )), 500

        @self.app.route('/api/cache/invalidate', methods=['POST'])
        def cache_invalidate():
            """Selectively invalidate cache entries based on criteria."""
            try:
                from .data_loader import DATA_CACHE, ARROW_DATASET_CACHE, CACHE_TIMESTAMPS, CACHE_EXPIRY, clear_arrow_cache
                from .validation import create_error_response
                import time
                
                # Get request parameters
                data = request.get_json() or {}
                instrument_pattern = data.get('instrument_pattern')
                timeframe_pattern = data.get('timeframe_pattern')
                max_age_seconds = data.get('max_age_seconds')
                force_all = data.get('force_all', False)
                
                if not any([instrument_pattern, timeframe_pattern, max_age_seconds, force_all]):
                    return jsonify(create_error_response(
                        "At least one invalidation criteria must be provided: instrument_pattern, timeframe_pattern, max_age_seconds, or force_all",
                        "request",
                        "MISSING_CRITERIA"
                    )), 400
                
                invalidated_count = 0
                current_time = time.time()
                
                # Collect keys to remove
                keys_to_remove = []
                
                # Check DATA_CACHE
                for cache_key in list(DATA_CACHE.keys()):
                    should_remove = False
                    
                    if force_all:
                        should_remove = True
                    else:
                        # Check age criteria
                        if max_age_seconds and cache_key in CACHE_TIMESTAMPS:
                            age = current_time - CACHE_TIMESTAMPS[cache_key]
                            if age > max_age_seconds:
                                should_remove = True
                        
                        # Check instrument pattern
                        if instrument_pattern and instrument_pattern.lower() in cache_key.lower():
                            should_remove = True
                        
                        # Check timeframe pattern
                        if timeframe_pattern and timeframe_pattern in cache_key:
                            should_remove = True
                    
                    if should_remove:
                        keys_to_remove.append(cache_key)
                
                # Remove identified cache entries
                for cache_key in keys_to_remove:
                    if cache_key in DATA_CACHE:
                        del DATA_CACHE[cache_key]
                        invalidated_count += 1
                    if cache_key in CACHE_TIMESTAMPS:
                        del CACHE_TIMESTAMPS[cache_key]
                
                # Handle ARROW_DATASET_CACHE separately
                arrow_keys_to_remove = []
                for cache_key in list(ARROW_DATASET_CACHE.keys()):
                    should_remove = False
                    
                    if force_all:
                        should_remove = True
                    else:
                        # Check instrument pattern
                        if instrument_pattern and instrument_pattern.lower() in cache_key.lower():
                            should_remove = True
                        
                        # Check timeframe pattern  
                        if timeframe_pattern and timeframe_pattern in cache_key:
                            should_remove = True
                    
                    if should_remove:
                        arrow_keys_to_remove.append(cache_key)
                
                # Remove arrow cache entries
                for cache_key in arrow_keys_to_remove:
                    if cache_key in ARROW_DATASET_CACHE:
                        del ARROW_DATASET_CACHE[cache_key]
                        invalidated_count += 1
                
                # Prepare response
                response = {
                    'invalidated_count': invalidated_count,
                    'criteria_used': {
                        'instrument_pattern': instrument_pattern,
                        'timeframe_pattern': timeframe_pattern,
                        'max_age_seconds': max_age_seconds,
                        'force_all': force_all
                    },
                    'remaining_cache_items': len(DATA_CACHE) + len(ARROW_DATASET_CACHE),
                    'message': f"Successfully invalidated {invalidated_count} cache entries"
                }
                
                return jsonify(response)
                
            except Exception as e:
                logger.error(f"Error invalidating cache: {e}")
                from .validation import create_error_response
                return jsonify(create_error_response(
                    "Failed to invalidate cache entries",
                    "server",
                    "CACHE_INVALIDATION_FAILED"
                )), 500

        @self.app.route('/api/cache/warm', methods=['POST'])
        def cache_warm():
            """Warm cache by pre-loading frequently accessed data."""
            try:
                from .data_loader import get_available_instruments, load_instrument_data_optimized
                from .validation import create_error_response
                
                # Get request parameters
                data = request.get_json() or {}
                instruments = data.get('instruments', [])
                timeframes = data.get('timeframes', ['1min'])
                limit = data.get('limit', 1000)
                
                # If no instruments specified, use all available
                if not instruments:
                    available_instruments = get_available_instruments()
                    instruments = [inst['id'] for inst in available_instruments[:5]]  # Limit to first 5 to avoid overload
                
                warmed_count = 0
                errors = []
                
                for instrument in instruments:
                    for timeframe in timeframes:
                        try:
                            # Load data to warm cache
                            df = load_instrument_data_optimized(
                                instrument_id=instrument,
                                timeframe=timeframe,
                                limit=limit,
                                use_cache=True
                            )
                            
                            if not df.empty:
                                warmed_count += 1
                                logger.info(f"Warmed cache for {instrument} {timeframe}: {len(df)} records")
                            
                        except Exception as e:
                            error_msg = f"Failed to warm cache for {instrument} {timeframe}: {str(e)}"
                            errors.append(error_msg)
                            logger.warning(error_msg)
                
                response = {
                    'warmed_count': warmed_count,
                    'total_attempts': len(instruments) * len(timeframes),
                    'success_rate_percent': round((warmed_count / max(len(instruments) * len(timeframes), 1)) * 100, 2),
                    'errors': errors,
                    'instruments_processed': instruments,
                    'timeframes_processed': timeframes,
                    'message': f"Successfully warmed {warmed_count} cache entries"
                }
                
                return jsonify(response)
                
            except Exception as e:
                logger.error(f"Error warming cache: {e}")
                from .validation import create_error_response
                return jsonify(create_error_response(
                    "Failed to warm cache",
                    "server",
                    "CACHE_WARMING_FAILED"
                )), 500

    def start_server(self) -> None:
        """Start the Flask server."""
        try:
            # Validate configuration
            if not self.config.validate():
                raise ValueError("Invalid configuration")

            # Create test data if requested
            if self.config.enable_test_data:
                self._create_test_data()

            # Log startup information
            logger.info(f"Starting Nautilus Trader Chart Server")
            logger.info(f"Server URL: {self.config.get_server_url()}")
            logger.info(f"Catalog path: {self.config.catalog_path}")
            logger.info(f"Debug mode: {self.config.debug}")

            # Start Flask server
            self.app.run(
                host=self.config.host,
                port=self.config.port,
                debug=self.config.debug,
                threaded=True
            )

        except Exception as e:
            logger.error(f"Error starting server: {e}")
            raise

    def run(self):
        """Run the Flask server."""
        try:
            self.app.run(
                host=self.config.host,
                port=self.config.port,
                debug=self.config.debug,
                threaded=True
            )
        except Exception as e:
            self.logger.error(f"Failed to start server: {e}")
            raise

    def stop_server(self) -> None:
        """Stop the Flask server."""
        # Flask doesn't have a built-in way to stop the server programmatically
        # This would typically be handled by the WSGI server in production
        logger.info("Server stop requested")

    def get_available_instruments(self) -> list:
        """Get available instruments from the data loader."""
        return get_available_instruments()

    def validate_data_source(self, path: str) -> bool:
        """Validate that the data source is accessible."""
        try:
            if not os.path.exists(path):
                return False

            # Check if it's a directory with the expected structure
            data_dir = os.path.join(path, "data", "bar")
            if not os.path.exists(data_dir):
                return False

            # Check if there are any instrument directories
            try:
                subdirs = [d for d in os.listdir(data_dir)
                          if os.path.isdir(os.path.join(data_dir, d))]
                return len(subdirs) > 0
            except OSError:
                return False

        except Exception as e:
            logger.error(f"Error validating data source {path}: {e}")
            return False

    def _create_test_data(self):
        """Create test data if it doesn't exist."""
        try:
            if not os.path.exists(self.config.catalog_path):
                logger.info("Creating test catalog with sample data")

                symbols = self.config.test_symbols.split(',')
                success = create_test_catalog(
                    catalog_path=self.config.catalog_path,
                    symbols=symbols,
                    bars_per_symbol=self.config.test_bars
                )

                if success:
                    logger.info(f"Test catalog created at {self.config.catalog_path}")
                else:
                    logger.error("Failed to create test catalog")
            else:
                logger.info(f"Using existing catalog at {self.config.catalog_path}")

        except Exception as e:
            logger.error(f"Error creating test data: {e}")

    def _health_check(self) -> dict:
        """Perform health check."""
        try:
            # Check if catalog path exists and is accessible
            catalog_accessible = self.validate_data_source(self.config.catalog_path)
            
            # Get available instruments count
            instruments = get_available_instruments()
            instrument_count = len(instruments)
            
            return {
                'status': 'healthy' if catalog_accessible else 'unhealthy',
                'catalog_path': self.config.catalog_path,
                'catalog_accessible': catalog_accessible,
                'instrument_count': instrument_count,
                'server_url': self.config.get_server_url()
            }
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return {
                'status': 'error',
                'error': str(e)
            }


def create_app(config=None):
    """
    Create and configure Flask application.

    Args:
        config: Chart configuration (optional)

    Returns:
        Configured Flask application
    """
    if config is None:
        config = ChartConfig()

    server = ChartServer(config)
    return server.app


def run_server(
    host: str = "0.0.0.0",
    port: int = 8080,
    catalog_path: str = "/tmp/test_catalog",
    debug: bool = False,
    log_level: str = "INFO",
    **kwargs
) -> None:
    """
    Run the chart server with specified configuration.

    Args:
        host: Host to bind to
        port: Port to listen on
        catalog_path: Path to the data catalog
        debug: Enable debug mode
        log_level: Logging level
        **kwargs: Additional configuration options
    """
    try:
        # Create configuration
        config_dict = {
            'host': host,
            'port': port,
            'catalog_path': catalog_path,
            'debug': debug,
            'log_level': log_level,
            **kwargs
        }

        config = ChartConfig(**config_dict)

        # Create and start server
        server = ChartServer(config)
        server.start_server()

    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server error: {e}")
        raise


if __name__ == "__main__":
    # Basic command line interface
    import argparse

    parser = argparse.ArgumentParser(description="Nautilus Trader Chart Server")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8080, help="Port to listen on")
    parser.add_argument("--catalog", default="/tmp/test_catalog", help="Catalog path")
    parser.add_argument("--debug", action="store_true", help="Enable debug mode")
    parser.add_argument("--log-level", default="INFO", help="Logging level")
    parser.add_argument("--test-data", action="store_true", help="Create test data")

    args = parser.parse_args()

    run_server(
        host=args.host,
        port=args.port,
        catalog_path=args.catalog,
        debug=args.debug,
        log_level=args.log_level,
        enable_test_data=args.test_data
    )
