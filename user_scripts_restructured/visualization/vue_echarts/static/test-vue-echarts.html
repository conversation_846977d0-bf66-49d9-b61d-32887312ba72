<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue-ECharts Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: white;
            font-family: Arial, sans-serif;
        }
        #app {
            width: 100%;
            height: 600px;
            border: 2px solid #00d4aa;
            background: #2d2d2d;
        }
        .chart {
            width: 100%;
            height: 100%;
        }
        .info {
            margin: 20px 0;
            padding: 10px;
            background: #333;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>Vue-ECharts Minimal Test</h1>
    <div class="info">
        <p>Status: <span id="status">Initializing...</span></p>
        <p>Vue Instance: <span id="vue-instance">None</span></p>
        <p>Chart Instance: <span id="chart-instance">None</span></p>
        <p>Error: <span id="error">None</span></p>
    </div>
    
    <div id="app"></div>

    <script type="module">
        console.log('🧪 Starting Vue-ECharts minimal test...');
        
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
            console.log('📊 Status:', message);
        }
        
        function updateError(error) {
            document.getElementById('error').textContent = error;
            console.error('❌ Error:', error);
        }
        
        try {
            updateStatus('Loading Vue and ECharts from CDN...');
            
            // Load Vue and ECharts from CDN
            const vueScript = document.createElement('script');
            vueScript.src = 'https://unpkg.com/vue@3/dist/vue.global.js';
            document.head.appendChild(vueScript);
            
            const echartsScript = document.createElement('script');
            echartsScript.src = 'https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js';
            document.head.appendChild(echartsScript);
            
            const vueEchartsScript = document.createElement('script');
            vueEchartsScript.src = 'https://unpkg.com/vue-echarts@6.6.1/dist/index.umd.min.js';
            document.head.appendChild(vueEchartsScript);
            
            // Wait for all scripts to load
            Promise.all([
                new Promise(resolve => vueScript.onload = resolve),
                new Promise(resolve => echartsScript.onload = resolve),
                new Promise(resolve => vueEchartsScript.onload = resolve)
            ]).then(() => {
                updateStatus('All libraries loaded, creating Vue app...');
                initVueApp();
            }).catch(error => {
                updateError('Failed to load libraries: ' + error.message);
            });
            
        } catch (error) {
            updateError('Initialization error: ' + error.message);
        }
        
        function initVueApp() {
            try {
                updateStatus('Creating Vue application...');
                
                const { createApp } = Vue;
                const VChart = VueECharts;
                
                const app = createApp({
                    components: {
                        VChart
                    },
                    data() {
                        return {
                            option: {
                                title: {
                                    text: 'Vue-ECharts Test Chart',
                                    left: 'center',
                                    textStyle: {
                                        color: '#ffffff'
                                    }
                                },
                                backgroundColor: '#1a1a1a',
                                xAxis: {
                                    type: 'category',
                                    data: ['09:30', '09:31', '09:32', '09:33', '09:34'],
                                    axisLine: { lineStyle: { color: '#404040' } },
                                    axisLabel: { color: '#cccccc' }
                                },
                                yAxis: {
                                    type: 'value',
                                    scale: true,
                                    axisLine: { lineStyle: { color: '#404040' } },
                                    axisLabel: { color: '#cccccc' },
                                    splitLine: { lineStyle: { color: '#333333' } }
                                },
                                series: [{
                                    name: 'Test Data',
                                    type: 'candlestick',
                                    data: [
                                        [21880, 21890, 21875, 21885],
                                        [21885, 21895, 21880, 21890],
                                        [21890, 21900, 21885, 21895],
                                        [21895, 21905, 21890, 21900],
                                        [21900, 21910, 21895, 21905]
                                    ],
                                    itemStyle: {
                                        color: '#00d4aa',
                                        color0: '#ff4444',
                                        borderColor: '#00d4aa',
                                        borderColor0: '#ff4444'
                                    }
                                }]
                            }
                        };
                    },
                    template: \`
                        <v-chart 
                            class="chart" 
                            :option="option" 
                            :autoresize="true"
                            :init-options="{
                                renderer: 'canvas',
                                devicePixelRatio: 1
                            }"
                        />
                    \`,
                    mounted() {
                        updateStatus('Vue app mounted successfully!');
                        document.getElementById('vue-instance').textContent = 'Created';
                        document.getElementById('chart-instance').textContent = 'Mounted';
                    }
                });
                
                app.mount('#app');
                updateStatus('Vue app created and mounting...');
                
            } catch (error) {
                updateError('Vue app creation error: ' + error.message);
            }
        }
    </script>
</body>
</html>
