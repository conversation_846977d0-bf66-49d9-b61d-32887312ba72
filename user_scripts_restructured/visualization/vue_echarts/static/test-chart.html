<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ECharts Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: white;
            font-family: Arial, sans-serif;
        }
        #chart-container {
            width: 100%;
            height: 600px;
            border: 2px solid #00d4aa;
            background: #2d2d2d;
        }
        .info {
            margin: 20px 0;
            padding: 10px;
            background: #333;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>ECharts Minimal Test</h1>
    <div class="info">
        <p>Status: <span id="status">Initializing...</span></p>
        <p>Chart Instance: <span id="chart-instance">None</span></p>
        <p>Error: <span id="error">None</span></p>
    </div>
    
    <div id="chart-container"></div>

    <script type="module">
        console.log('🧪 Starting ECharts minimal test...');
        
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
            console.log('📊 Status:', message);
        }
        
        function updateError(error) {
            document.getElementById('error').textContent = error;
            console.error('❌ Error:', error);
        }
        
        try {
            updateStatus('Loading ECharts from CDN...');
            
            // Load ECharts from CDN
            import('https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js')
                .then(() => {
                    updateStatus('ECharts loaded from CDN');
                    initChart(window.echarts);
                })
                .catch(error => {
                    updateError('Failed to load ECharts: ' + error.message);
                });
            
        } catch (error) {
            updateError('Initialization error: ' + error.message);
        }
        
        function initChart(echarts) {
            try {
                updateStatus('Initializing chart...');
                
                const container = document.getElementById('chart-container');
                if (!container) {
                    throw new Error('Chart container not found');
                }
                
                // Create chart instance
                const chart = echarts.init(container, 'dark');
                document.getElementById('chart-instance').textContent = 'Created';
                
                // Simple test data
                const testData = [
                    [21880, 21890, 21875, 21885],
                    [21885, 21895, 21880, 21890],
                    [21890, 21900, 21885, 21895],
                    [21895, 21905, 21890, 21900],
                    [21900, 21910, 21895, 21905]
                ];
                
                const option = {
                    title: {
                        text: 'ECharts Test Chart',
                        left: 'center',
                        textStyle: {
                            color: '#ffffff'
                        }
                    },
                    backgroundColor: '#1a1a1a',
                    xAxis: {
                        type: 'category',
                        data: ['09:30', '09:31', '09:32', '09:33', '09:34'],
                        axisLine: { lineStyle: { color: '#404040' } },
                        axisLabel: { color: '#cccccc' }
                    },
                    yAxis: {
                        type: 'value',
                        scale: true,
                        axisLine: { lineStyle: { color: '#404040' } },
                        axisLabel: { color: '#cccccc' },
                        splitLine: { lineStyle: { color: '#333333' } }
                    },
                    series: [{
                        name: 'Test Data',
                        type: 'candlestick',
                        data: testData,
                        itemStyle: {
                            color: '#00d4aa',
                            color0: '#ff4444',
                            borderColor: '#00d4aa',
                            borderColor0: '#ff4444'
                        }
                    }]
                };
                
                updateStatus('Setting chart option...');
                chart.setOption(option);
                
                updateStatus('Chart rendered successfully!');
                
                // Resize chart after a short delay
                setTimeout(() => {
                    chart.resize();
                    updateStatus('Chart resized');
                }, 100);
                
            } catch (error) {
                updateError('Chart initialization error: ' + error.message);
            }
        }
    </script>
</body>
</html>
