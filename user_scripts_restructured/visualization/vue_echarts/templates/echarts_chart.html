<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ECharts - {{ instrument }} - Nautilus Trader</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #1e1e1e;
            color: #ffffff;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .chart-container {
            height: 80vh;
            margin: 20px 0;
            border: 1px solid #333;
            border-radius: 8px;
            overflow: hidden;
        }
        .navbar {
            background-color: #2d2d2d !important;
        }
        .navbar-brand {
            color: #ffffff !important;
            font-weight: bold;
        }
        .btn-outline-light {
            border-color: #6c757d;
        }
        .btn-outline-light:hover {
            background-color: #6c757d;
            border-color: #6c757d;
        }
        .status-indicator {
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            margin: 10px 0;
        }
        .status-connected {
            background-color: #1e7e34;
            color: white;
        }
        .status-connecting {
            background-color: #856404;
            color: white;
        }
        .status-disconnected {
            background-color: #721c24;
            color: white;
        }
        .loading-indicator {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #ffffff;
            font-size: 16px;
        }
        .controls-panel {
            background-color: #2d2d2d;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .control-group {
            margin-bottom: 10px;
        }
        .control-group label {
            color: #cccccc;
            margin-right: 10px;
            min-width: 120px;
            display: inline-block;
        }
        .form-select, .form-control {
            background-color: #3d3d3d;
            border-color: #555;
            color: #ffffff;
        }
        .form-select:focus, .form-control:focus {
            background-color: #3d3d3d;
            border-color: #007bff;
            color: #ffffff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        .error-message {
            background-color: #721c24;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            display: none;
        }
        .performance-info {
            background-color: #2d2d2d;
            padding: 10px 15px;
            border-radius: 8px;
            margin-top: 10px;
            font-size: 12px;
            color: #cccccc;
        }
        .performance-metric {
            display: inline-block;
            margin-right: 20px;
        }
        .performance-metric strong {
            color: #ffffff;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">📈 Nautilus Trader - ECharts</a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">← Back to Index</a>
                <a class="nav-link btn btn-outline-light ms-2" href="/vue/chart/{{ instrument }}">Vue-ECharts Advanced</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <!-- Status and Controls -->
        <div class="row">
            <div class="col-12">
                <div id="connection-status" class="status-indicator status-connecting">
                    🔄 Connecting to server...
                </div>
                
                <div class="controls-panel">
                    <h5>📊 Chart Configuration</h5>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="control-group">
                                <label for="timeframe-select">Timeframe:</label>
                                <select id="timeframe-select" class="form-select">
                                    <option value="1min">1 Minute</option>
                                    <option value="5min">5 Minutes</option>
                                    <option value="15min">15 Minutes</option>
                                    <option value="1hour">1 Hour</option>
                                    <option value="1day">1 Day</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="control-group">
                                <label for="data-points-input">Max Data Points:</label>
                                <input type="number" id="data-points-input" class="form-control" value="10000" min="1000" max="50000" step="1000">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="control-group">
                                <label for="streaming-toggle">Real-time Updates:</label>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="streaming-toggle" checked>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="control-group">
                                <button id="refresh-btn" class="btn btn-outline-light">🔄 Refresh Data</button>
                                <button id="load-historical-btn" class="btn btn-outline-info ms-2">⬅️ Historical</button>
                                <button id="load-recent-btn" class="btn btn-outline-warning ms-2">➡️ Recent</button>
                                <button id="export-btn" class="btn btn-outline-success ms-2">💾 Export</button>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="error-message" class="error-message"></div>
            </div>
        </div>

        <!-- Chart Container -->
        <div class="row">
            <div class="col-12">
                <div id="chart-container" class="chart-container">
                    <div class="loading-indicator">
                        <div class="spinner-border text-light" role="status">
                            <span class="visually-hidden">Loading chart...</span>
                        </div>
                        <div class="mt-2">Loading {{ instrument }} chart data...</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Info -->
        <div class="row">
            <div class="col-12">
                <div id="performance-info" class="performance-info">
                    <div class="performance-metric">
                        <strong>Data Points:</strong> <span id="data-count">-</span>
                    </div>
                    <div class="performance-metric">
                        <strong>Memory Usage:</strong> <span id="memory-usage">-</span>
                    </div>
                    <div class="performance-metric">
                        <strong>Last Update:</strong> <span id="last-update">-</span>
                    </div>
                    <div class="performance-metric">
                        <strong>Loading Performance:</strong> <span id="load-time">-</span>
                    </div>
                    <div class="performance-metric">
                        <strong>Chart Library:</strong> <span>Apache ECharts 5.5.0</span>
                    </div>
                    <div class="performance-metric">
                        <strong>Infinite Scroll:</strong> <span id="infinite-scroll-status">Enabled</span>
                    </div>
                    <div class="performance-metric">
                        <strong>Memory Pressure:</strong> <span id="memory-pressure">Low</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        import { io } from 'https://cdn.socket.io/4.7.2/socket.io.esm.min.js';
        import { 
            initializeChart, 
            updateChart, 
            startStreaming, 
            stopStreaming,
            resizeChart,
            getChartState,
            destroyChart
        } from '/static/js/echarts-chart.js';

        // Chart configuration
        const instrument = '{{ instrument }}';
        const config = {
            enabled: true,
            chunkSize: 500,
            loadThreshold: 20,
            maxDataPoints: parseInt(document.getElementById('data-points-input').value),
            enableStreaming: true,
            streamingInterval: 1000
        };

        // UI elements
        const connectionStatus = document.getElementById('connection-status');
        const errorMessage = document.getElementById('error-message');
        const timeframeSelect = document.getElementById('timeframe-select');
        const dataPointsInput = document.getElementById('data-points-input');
        const streamingToggle = document.getElementById('streaming-toggle');
        const refreshBtn = document.getElementById('refresh-btn');
        const loadHistoricalBtn = document.getElementById('load-historical-btn');
        const loadRecentBtn = document.getElementById('load-recent-btn');
        const exportBtn = document.getElementById('export-btn');
        const performanceInfo = document.getElementById('performance-info');

        // Performance tracking
        let loadStartTime = Date.now();
        let chartManager = null;

        // Initialize chart
        async function initChart() {
            try {
                loadStartTime = Date.now();
                updateStatus('connecting', '🔄 Initializing ECharts...');

                chartManager = await initializeChart(
                    'chart-container',
                    instrument,
                    timeframeSelect.value,
                    config
                );

                updateStatus('connected', '✅ ECharts loaded successfully');
                updatePerformanceInfo();

                // Start streaming if enabled
                if (streamingToggle.checked) {
                    startStreaming();
                }

            } catch (error) {
                console.error('Chart initialization failed:', error);
                showError(`Failed to initialize chart: ${error.message}`);
                updateStatus('disconnected', '❌ Failed to load chart');
            }
        }

        // Update connection status
        function updateStatus(status, message) {
            connectionStatus.className = `status-indicator status-${status}`;
            connectionStatus.textContent = message;
        }

        // Show error message
        function showError(message) {
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
            setTimeout(() => {
                errorMessage.style.display = 'none';
            }, 5000);
        }

        // Update performance information
        function updatePerformanceInfo() {
            if (!chartManager) return;

            const state = getChartState();
            const loadTime = Date.now() - loadStartTime;

            document.getElementById('data-count').textContent = state?.dataCount || '-';
            document.getElementById('memory-usage').textContent = 
                state?.dataCount ? `${Math.round(state.dataCount * 0.1)}KB` : '-';
            document.getElementById('last-update').textContent = 
                state?.lastUpdate ? new Date(state.lastUpdate).toLocaleTimeString() : '-';
            document.getElementById('load-time').textContent = `${loadTime}ms`;
            
            // Update infinite scroll information
            if (chartManager.getInfiniteScrollState) {
                const scrollState = chartManager.getInfiniteScrollState();
                const scrollMetrics = chartManager.getInfiniteScrollMetrics();
                
                if (scrollState) {
                    const statusText = scrollState.isLoadingHistorical || scrollState.isLoadingRecent ? 'Loading...' : 'Ready';
                    document.getElementById('infinite-scroll-status').textContent = statusText;
                    
                    const pressureColors = {
                        'low': '#26a69a',
                        'moderate': '#ffa726', 
                        'high': '#ff7043',
                        'critical': '#ef5350'
                    };
                    
                    const memoryPressureEl = document.getElementById('memory-pressure');
                    memoryPressureEl.textContent = scrollState.memoryPressure.charAt(0).toUpperCase() + scrollState.memoryPressure.slice(1);
                    memoryPressureEl.style.color = pressureColors[scrollState.memoryPressure] || '#ffffff';
                }
            }
        }

        // Event listeners
        timeframeSelect.addEventListener('change', () => {
            if (chartManager) {
                destroyChart();
                initChart();
            }
        });

        dataPointsInput.addEventListener('change', () => {
            config.maxDataPoints = parseInt(dataPointsInput.value);
            if (chartManager) {
                // Reinitialize with new config
                destroyChart();
                initChart();
            }
        });

        streamingToggle.addEventListener('change', () => {
            if (!chartManager) return;
            
            if (streamingToggle.checked) {
                startStreaming();
                updateStatus('connected', '✅ Real-time streaming enabled');
            } else {
                stopStreaming();
                updateStatus('connected', '✅ Real-time streaming disabled');
            }
        });

        refreshBtn.addEventListener('click', () => {
            if (chartManager) {
                destroyChart();
                initChart();
            }
        });

        loadHistoricalBtn.addEventListener('click', () => {
            if (chartManager && chartManager.getInfiniteScrollManager) {
                const scrollManager = chartManager.getInfiniteScrollManager();
                if (scrollManager) {
                    scrollManager.loadHistoricalData();
                    updateStatus('connected', '⬅️ Loading historical data...');
                }
            }
        });

        loadRecentBtn.addEventListener('click', () => {
            if (chartManager && chartManager.getInfiniteScrollManager) {
                const scrollManager = chartManager.getInfiniteScrollManager();
                if (scrollManager) {
                    scrollManager.loadRecentData();
                    updateStatus('connected', '➡️ Loading recent data...');
                }
            }
        });

        exportBtn.addEventListener('click', () => {
            if (chartManager) {
                // Implement export functionality
                console.log('Export functionality to be implemented');
                showError('Export functionality coming soon!');
            }
        });

        // Handle window resize
        window.addEventListener('resize', () => {
            if (chartManager) {
                resizeChart();
            }
        });

        // Handle page unload
        window.addEventListener('beforeunload', () => {
            if (chartManager) {
                destroyChart();
            }
        });

        // Update performance info periodically
        setInterval(updatePerformanceInfo, 2000);

        // Initialize chart when page loads
        document.addEventListener('DOMContentLoaded', () => {
            // Set default timeframe if provided
            const urlParams = new URLSearchParams(window.location.search);
            const timeframe = urlParams.get('timeframe');
            if (timeframe) {
                timeframeSelect.value = timeframe;
            }

            initChart();
        });
    </script>
</body>
</html>