<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nautilus Trader - Vue.js ECharts Visualization</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1a1a1a;
            color: #ffffff;
            overflow: hidden;
        }
        
        #app {
            height: 100vh;
            width: 100vw;
        }
        
        /* Loading spinner for initial app load */
        .app-loading {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: #1a1a1a;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        
        .app-loading .spinner {
            width: 60px;
            height: 60px;
            border: 6px solid #404040;
            border-top: 6px solid #00d4aa;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 1.5rem;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .app-loading .loading-text {
            color: #cccccc;
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
        }
        
        .app-loading .loading-subtitle {
            color: #888888;
            font-size: 0.9rem;
        }
        
        /* Fallback message for browsers without JavaScript */
        .no-js {
            display: none;
            text-align: center;
            padding: 2rem;
            background: #2d2d2d;
            border: 1px solid #ff4444;
            border-radius: 8px;
            margin: 2rem;
        }
        
        .no-js h2 {
            color: #ff4444;
            margin-bottom: 1rem;
        }
        
        .no-js p {
            color: #cccccc;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <!-- Vue.js Application Mount Point -->
    <div id="app">
        <!-- Loading state while Vue.js initializes -->
        <div class="app-loading">
            <div class="spinner"></div>
            <div class="loading-text">Loading Nautilus Trader</div>
            <div class="loading-subtitle">Vue.js + ECharts Visualization</div>
        </div>
    </div>
    
    <!-- Fallback for browsers without JavaScript -->
    <noscript>
        <div class="no-js">
            <h2>JavaScript Required</h2>
            <p>
                This application requires JavaScript to be enabled in your browser.
                Please enable JavaScript and refresh the page to continue.
            </p>
        </div>
    </noscript>
    
    <!-- Vue.js Application Bundle -->
    <script type="module">
        // Global configuration
        window.NAUTILUS_CONFIG = {
            instrument: '{{ instrument_id }}',
            baseURL: window.location.origin,
            theme: 'dark',
            debug: {{ 'true' if debug else 'false' }},
            websocket: {
                enabled: true,
                reconnectAttempts: 5,
                reconnectDelay: 1000
            },
            chart: {
                enableInfiniteScroll: true,
                maxDataPoints: 10000,
                loadChunkSize: 100,
                samplingStrategy: 'adaptive',
                memoryThreshold: 80
            },
            performance: {
                enableMonitoring: true,
                fpsTarget: 60,
                memoryWarningThreshold: 70,
                memoryCriticalThreshold: 85
            }
        };
        
        // Error handling for module loading
        window.addEventListener('error', (event) => {
            console.error('Application error:', event.error);
            
            // Show error message
            const appElement = document.getElementById('app');
            if (appElement) {
                appElement.innerHTML = `
                    <div style="padding: 2rem; text-align: center; background: #2d2d2d; margin: 2rem; border-radius: 8px; border: 1px solid #ff4444;">
                        <h2 style="color: #ff4444; margin-bottom: 1rem;">Application Error</h2>
                        <p style="color: #cccccc; margin-bottom: 1rem;">Failed to load the visualization application.</p>
                        <p style="color: #888888; font-size: 0.9rem;">Error: ${event.error?.message || 'Unknown error'}</p>
                        <button onclick="location.reload()" style="margin-top: 1rem; padding: 0.5rem 1rem; background: #00d4aa; color: #000; border: none; border-radius: 4px; cursor: pointer;">
                            Retry
                        </button>
                    </div>
                `;
            }
        });
        
        // Enhanced Vue.js application loading with debug info
        console.log('🚀 Starting Vue.js application loading...');
        console.log('📍 Mount target #app exists:', !!document.getElementById('app'));
        console.log('🌍 NAUTILUS_CONFIG:', window.NAUTILUS_CONFIG);
        
        import('/static/js/vue-echarts-app.js?v=' + Date.now())
            .then(module => {
                console.log('✅ Vue.js application module loaded successfully');
                console.log('📦 Module exports:', Object.keys(module));
                
                // Remove loading spinner after a short delay to ensure Vue is mounted
                setTimeout(() => {
                    const loadingElement = document.querySelector('.app-loading');
                    if (loadingElement) {
                        loadingElement.remove();
                        console.log('🔄 Loading spinner removed');
                    }
                }, 1000);
            })
            .catch(error => {
                console.error('💥 Failed to load Vue.js application:', error);
                console.error('Stack trace:', error.stack);
                
                // Show detailed error to user
                const appElement = document.getElementById('app');
                if (appElement) {
                    appElement.innerHTML = `
                        <div style="padding: 2rem; text-align: center; background: #2d2d2d; margin: 2rem; border-radius: 8px; border: 1px solid #ff4444;">
                            <h2 style="color: #ff4444; margin-bottom: 1rem;">Vue.js Module Error</h2>
                            <p style="color: #cccccc; margin-bottom: 1rem;">Failed to load Vue.js application module.</p>
                            <details style="text-align: left; margin-bottom: 1rem;">
                                <summary style="color: #cccccc; cursor: pointer; margin-bottom: 0.5rem;">Error Details</summary>
                                <pre style="color: #ff6b6b; font-size: 0.9rem; background: #1a1a1a; padding: 1rem; border-radius: 4px; overflow-x: auto;">${error.message}</pre>
                                <pre style="color: #888888; font-size: 0.8rem; background: #1a1a1a; padding: 1rem; border-radius: 4px; overflow-x: auto; margin-top: 0.5rem;">${error.stack}</pre>
                            </details>
                            <button onclick="location.reload()" style="padding: 0.5rem 1rem; background: #00d4aa; color: #000; border: none; border-radius: 4px; cursor: pointer;">
                                Retry
                            </button>
                        </div>
                    `;
                }
                
                // Trigger error handler
                window.dispatchEvent(new ErrorEvent('error', {
                    error: new Error(`Module loading failed: ${error.message}`)
                }));
            });
    </script>
    
    <!-- Development tools (only in debug mode) -->
    {% if debug %}
    <script>
        // Development utilities
        window.__VUE_DEVTOOLS__ = true;
        
        // Performance monitoring in development
        if ('performance' in window && 'measure' in performance) {
            window.addEventListener('load', () => {
                console.log('Page load performance:', {
                    navigation: performance.getEntriesByType('navigation')[0],
                    paint: performance.getEntriesByType('paint')
                });
            });
        }
        
        // Memory monitoring in development
        if ('memory' in performance) {
            setInterval(() => {
                const memory = performance.memory;
                console.log('Memory usage:', {
                    used: Math.round(memory.usedJSHeapSize / 1024 / 1024) + ' MB',
                    total: Math.round(memory.totalJSHeapSize / 1024 / 1024) + ' MB',
                    limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024) + ' MB'
                });
            }, 30000); // Every 30 seconds
        }
    </script>
    {% endif %}
</body>
</html>