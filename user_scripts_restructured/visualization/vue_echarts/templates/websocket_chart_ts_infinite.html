<!DOCTYPE html>
<html>
<head>
    <title>{{ instrument_id }} - Infinite Scroll Chart (TypeScript)</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Vue-ECharts components loaded via ES modules - no external dependencies needed -->
    <style>
        body {
            padding: 20px;
            background-color: #ffffff;
            color: #191919;
        }
        #loading {
            display: none;
            background-color: #f8f9fa;
            color: #191919;
            border-color: #dee2e6;
        }
        .btn-secondary {
            background-color: #f8f9fa;
            border-color: #dee2e6;
            color: #191919;
        }
        .btn-secondary:hover {
            background-color: #e9ecef;
        }
        h2 {
            color: #191919;
        }
        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }
        .alert-success {
            background-color: #d1edff;
            color: #084298;
            border-color: #b6d4fe;
        }
        .alert-info {
            background-color: #cff4fc;
            color: #055160;
            border-color: #b3e5fc;
        }
        .timeframe-selector {
            display: inline-block;
            margin-left: 20px;
        }
        .timeframe-btn {
            background-color: #f8f9fa;
            border-color: #dee2e6;
            color: #191919;
            margin-right: 5px;
        }
        .timeframe-btn.active {
            background-color: #0d6efd;
            border-color: #0d6efd;
            color: white;
        }
        #chart-container {
            height: 600px;
            position: relative;
        }
        #volume-container {
            height: 150px;
            position: relative;
        }
        .chart-info {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 10px;
            font-size: 0.9em;
        }
        .chart-info .info-item {
            display: inline-block;
            margin-right: 20px;
        }
        .connection-status {
            position: fixed;
            top: 10px;
            right: 10px;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 0.8em;
            z-index: 1000;
        }
        .connected {
            background-color: #d1edff;
            color: #084298;
            border: 1px solid #b6d4fe;
        }
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        /* Infinite Scroll Specific Styles */
        .infinite-scroll-controls {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .infinite-scroll-controls h5 {
            color: white;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }
        
        .infinite-scroll-controls h5::before {
            content: "∞";
            font-size: 1.5em;
            margin-right: 8px;
            color: #ffd700;
        }
        
        .infinite-scroll-loading {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 1000;
            background: rgba(0,0,0,0.85);
            color: white;
            padding: 10px 15px;
            border-radius: 6px;
            font-size: 12px;
            pointer-events: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
            backdrop-filter: blur(5px);
        }
        
        .infinite-scroll-loading .loading-historical,
        .infinite-scroll-loading .loading-recent {
            display: none;
            padding: 5px 0;
        }
        
        .infinite-scroll-loading .spinner {
            display: inline-block;
            width: 12px;
            height: 12px;
            border: 2px solid transparent;
            border-top: 2px solid #fff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        #infinite-scroll-status {
            display: none;
            position: fixed;
            bottom: 20px;
            right: 20px;
            max-width: 300px;
            z-index: 1001;
            font-size: 0.85em;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .memory-stats {
            background-color: rgba(0,0,0,0.05);
            border-radius: 4px;
            padding: 8px;
            margin-top: 10px;
            font-size: 0.8em;
        }
        
        .memory-stats .stat {
            display: inline-block;
            margin-right: 15px;
            color: rgba(255,255,255,0.9);
        }
        
        .debug-controls {
            background-color: #2c3e50;
            color: white;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            font-size: 0.85em;
        }
        
        .debug-controls button {
            font-size: 0.75em;
            padding: 2px 8px;
            margin-right: 5px;
        }
        
        .feature-badge {
            background: linear-gradient(45deg, #ff6b6b, #ffa500);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.7em;
            font-weight: bold;
            margin-left: 10px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
            .infinite-scroll-controls {
                padding: 10px;
            }
            .memory-stats .stat {
                display: block;
                margin-right: 0;
                margin-bottom: 5px;
            }
            #infinite-scroll-status {
                right: 10px;
                bottom: 10px;
                max-width: calc(100vw - 20px);
            }
        }
    </style>
</head>
<body>
    <div class="connection-status disconnected" id="connection-status">Disconnected</div>
    
    <div class="container-fluid">
        <div class="row mb-3">
            <div class="col">
                <a href="/websocket" class="btn btn-secondary">&larr; Back to Instruments</a>
                <h2 class="d-inline-block ms-3">
                    {{ instrument_id }} WebSocket Chart
                    <span class="feature-badge">∞ Infinite Scroll</span>
                </h2>
                
                <div class="timeframe-selector">
                    <button class="btn timeframe-btn active" data-timeframe="1min">1m</button>
                    <button class="btn timeframe-btn" data-timeframe="5min">5m</button>
                    <button class="btn timeframe-btn" data-timeframe="15min">15m</button>
                    <button class="btn timeframe-btn" data-timeframe="30min">30m</button>
                    <button class="btn timeframe-btn" data-timeframe="1h">1h</button>
                    <button class="btn timeframe-btn" data-timeframe="4h">4h</button>
                    <button class="btn timeframe-btn" data-timeframe="1d">1d</button>
                </div>
            </div>
        </div>

        <!-- Infinite Scroll Information Panel -->
        <div class="infinite-scroll-controls">
            <h5>Infinite Scrolling Active</h5>
            <p class="mb-2">Scroll to the edges of the chart to automatically load more historical or recent data. The chart intelligently manages memory and loading for optimal performance.</p>
            
            <div class="infinite-scroll-guidance mb-3">
                <p><strong>Navigation Tips:</strong></p>
                <ul class="small">
                    <li>📱 <strong>Click chart first</strong> to enable keyboard navigation</li>
                    <li>⬅️ <strong>Home key</strong> - Jump to historical data start</li>
                    <li>🔄 <strong>Scroll left edge</strong> - Auto-load historical data</li>
                    <li>🐛 <strong>Debug:</strong> <code>triggerInfiniteScroll()</code> in console</li>
                </ul>
            </div>
            
            <div class="memory-stats" id="memory-stats">
                <div class="stat">Bars Loaded: <strong id="total-bars">-</strong></div>
                <div class="stat">Memory: <strong id="memory-usage">-</strong></div>
                <div class="stat">Loaded Ranges: <strong id="loaded-ranges">-</strong></div>
                <div class="stat">Cache Hits: <strong id="cache-hits">-</strong></div>
                <div class="stat">Infinite Scroll: <strong id="infinite-scroll-state">-</strong></div>
            </div>
            
            <div class="debug-controls" id="debug-controls" style="display: none;">
                <strong>Debug Controls:</strong>
                <button class="btn btn-sm btn-outline-light" onclick="wsChartController?.enableDebugMode(true)">Enable Debug</button>
                <button class="btn btn-sm btn-outline-light" onclick="console.log(wsChartController?.getInfiniteScrollMetrics())">Show Metrics</button>
                <button class="btn btn-sm btn-outline-light" onclick="console.log(wsChartController?.getMemoryStats())">Memory Stats</button>
                <button class="btn btn-sm btn-outline-light" onclick="wsChartController?.updateInfiniteScrollConfig({loadTriggerDistance: 100})">Increase Trigger</button>
            </div>
        </div>

        <div id="loading" class="alert alert-info">
            <div class="d-flex align-items-center">
                <div class="spinner-border spinner-border-sm me-2" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                Connecting and loading initial chart data...
            </div>
        </div>

        <div id="error-message" class="alert alert-danger" style="display: none;"></div>

        <!-- Infinite Scroll Status (floating) -->
        <div id="infinite-scroll-status" class="alert alert-info"></div>

        <div id="chart-info" class="chart-info" style="display: none;">
            <div class="info-item"><strong>Bars:</strong> <span id="bars-count">-</span></div>
            <div class="info-item"><strong>Date Range:</strong> <span id="date-range">-</span></div>
            <div class="info-item"><strong>Data Quality:</strong> <span id="data-quality">-</span></div>
            <div class="info-item"><strong>Updates:</strong> <span id="update-status">Waiting...</span></div>
            <div class="info-item"><strong>Infinite Scroll:</strong> <span id="infinite-scroll-state" class="text-success">Active</span></div>
        </div>

        <!-- Chart containers with loading indicators -->
        <div id="chart-container">
            <!-- Loading indicators will be dynamically inserted here -->
        </div>
        <div id="volume-container"></div>
    </div>

    <!-- Hidden data for TypeScript -->
    <div id="instrument-data" data-instrument-id="{{ instrument_id }}" style="display: none;"></div>

    <!-- Global configuration for infinite scrolling -->
    <script>
        // Global configuration that TypeScript can access
        window.chartDebugMode = {{ 'true' if debug else 'false' }};
        window.infiniteScrollConfig = {
            loadTriggerDistance: 5,  // Match TypeScript default
            chunkSize: 500,
            maxBarsInMemory: 5000,
            showLoadingIndicators: true,
            bidirectionalLoading: true,
            enableMemoryManagement: true
        };
        
        // URL parameters for debugging
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('debug') === 'true') {
            document.getElementById('debug-controls').style.display = 'block';
        }
        
        // Update memory stats periodically
        function updateMemoryStats() {
            if (window.wsChartController) {
                const stats = window.wsChartController.getMemoryStats();
                const metrics = window.wsChartController.getInfiniteScrollMetrics();
                
                if (stats) {
                    document.getElementById('total-bars').textContent = stats.totalBars.toLocaleString();
                    document.getElementById('memory-usage').textContent = stats.memoryUsageEstimateMB.toFixed(1) + ' MB';
                    document.getElementById('loaded-ranges').textContent = stats.loadedRanges.length.toString();
                }
                
                if (metrics) {
                    const hitRate = metrics.totalLoadRequests > 0 ? 
                        (metrics.cacheHitRate * 100).toFixed(1) + '%' : '-';
                    document.getElementById('cache-hits').textContent = hitRate;
                }

                // Add infinite scroll state monitoring with focus tracking
                const infiniteScrollStateEl = document.getElementById('infinite-scroll-state');
                if (infiniteScrollStateEl) {
                    const chartContainer = document.getElementById('chart-container');
                    const isFocused = document.activeElement === chartContainer;
                    infiniteScrollStateEl.innerHTML = `Active (Focus: ${isFocused ? '✅' : '❌'})`;
                }
            }
        }
        
        // Update every 5 seconds
        setInterval(updateMemoryStats, 5000);
        
        // Initial update after a delay
        setTimeout(updateMemoryStats, 2000);
    </script>

    <!-- TypeScript ES Module Loading -->
    
    <!-- Load TypeScript ES Module -->
    <script type="module">
        try {
            console.log('Loading TypeScript chart module with bundled Socket.IO...');
            // Import the compiled TypeScript chart module (includes bundled Socket.IO)
            const module = await import('{{ url_for("static", filename="js/websocket-chart.js") }}');
            console.log('TypeScript chart module loaded successfully with ES modules');
            
            // Manually trigger initialization since DOMContentLoaded has already fired
            console.log('Manually initializing chart since DOM is already loaded...');
            
            // Get instrument ID from the hidden data element
            const instrumentDataEl = document.getElementById('instrument-data');
            const instrumentId = instrumentDataEl?.dataset.instrumentId || '{{ instrument_id }}';
            
            if (!instrumentId) {
                throw new Error('Instrument ID not found');
            }
            
            // Get debug mode from URL
            const urlParams = new URLSearchParams(window.location.search);
            const debugMode = urlParams.get('debug') === 'true';
            
            console.log(`Initializing chart for instrument: ${instrumentId}, debug: ${debugMode}`);
            
            // Call the exported initialization function
            if (module.initializeChart && typeof module.initializeChart === 'function') {
                await module.initializeChart();
                console.log('Chart initialized successfully via exported function');
            } else {
                console.warn('initializeChart function not found in module, trying fallback...');
                // The module should have auto-initialized, just log status
                console.log('Relying on auto-initialization from module');
            }
            
        } catch (error) {
            console.error('Failed to load chart module:', error);
            // Show error to user
            const errorEl = document.getElementById('error-message');
            if (errorEl) {
                errorEl.textContent = `Failed to load chart: ${error.message}`;
                errorEl.style.display = 'block';
            }
        }
    </script>
</body>
</html>