<!DOCTYPE html>
<html>
<head>
    <title>Nautilus Trader Chart Server</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
            background-color: #ffffff;
        }
        .card {
            transition: transform 0.2s;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .instrument-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px 10px 0 0;
            margin-bottom: 30px;
            padding: 40px 0;
        }
        .btn-outline-primary {
            border-color: #0d6efd;
            color: #0d6efd;
        }
        .btn-outline-primary:hover {
            background-color: #0d6efd;
            border-color: #0d6efd;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="instrument-header text-center">
            <h1 class="display-4 mb-3">📈 Nautilus Trader Charts</h1>
            <p class="lead">Interactive Financial Data Visualization</p>
        </div>

        <div id="loading" class="alert alert-info" style="display: none;">
            <div class="d-flex align-items-center">
                <div class="spinner-border spinner-border-sm me-2" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                Loading instruments...
            </div>
        </div>

        <div id="error-message" class="alert alert-danger" style="display: none;"></div>

        <div id="instrument-list">
            <!-- Instruments will be loaded here by TypeScript -->
        </div>

        <div class="row mt-5">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center">
                        <h5 class="card-title">Chart Features</h5>
                        <div class="row">
                            <div class="col-md-3">
                                <h6>📊 Interactive Charts</h6>
                                <p class="small text-muted">Professional Vue-ECharts visualization with advanced interactivity</p>
                            </div>
                            <div class="col-md-3">
                                <h6>⏱️ Multiple Timeframes</h6>
                                <p class="small text-muted">1min to 1day timeframes with instant switching</p>
                            </div>
                            <div class="col-md-3">
                                <h6>📈 Volume Analysis</h6>
                                <p class="small text-muted">Synchronized volume charts with price action</p>
                            </div>
                            <div class="col-md-3">
                                <h6>🚀 WebSocket Support</h6>
                                <p class="small text-muted">Real-time data streaming for live charts</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Load compiled TypeScript -->
    <script type="module" src="{{ url_for('static', filename='js/index.js') }}"></script>
</body>
</html>