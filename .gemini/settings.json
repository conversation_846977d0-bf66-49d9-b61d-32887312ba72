{"mcpServers": {"time": {"command": "uvx", "args": ["mcp-server-time", "--local-timezone=America/New_York"], "env": {}}, "Context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"]}, "fetch": {"command": "uvx", "args": ["mcp-server-fetch"]}, "playwright": {"command": "npx", "args": ["-y", "@playwright/mcp@latest"]}, "serena": {"type": "stdio", "command": "uvx", "args": ["--from", "git+https://github.com/oraios/serena", "serena-mcp-server", "--context", "ide-assistant", "--project", "/home/<USER>/nautilus_trader_fork"], "env": {}}}}