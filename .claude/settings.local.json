{"permissions": {"allow": ["Bash(find:*)", "<PERSON>sh(claude config show)", "<PERSON><PERSON>(claude mcp:*)", "mcp__time__get_current_time", "mcp__filesystem__list_allowed_directories", "mcp__memory__search_nodes", "mcp__fetch__fetch", "mcp__code-reasoning__code-reasoning", "Bash(grep:*)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(timeout:*)", "mcp__playwright__browser_navigate", "Bash(kill:*)", "mcp__playwright__browser_click", "mcp__playwright__browser_console_messages", "mcp__playwright__browser_wait_for", "Bash(ls:*)", "<PERSON><PERSON>(uv run:*)", "mcp__basic-memory__search_notes", "mcp__basic-memory__recent_activity", "mcp__basic-memory__write_note", "mcp__filesystem__directory_tree", "mcp__filesystem__read_multiple_files", "mcp__basic-memory__edit_note", "mcp__basic-memory__delete_note", "Bash(pgrep:*)", "mcp__filesystem__list_directory", "mcp__filesystem__read_file", "mcp__filesystem__search_files", "Bash(rg:*)", "mcp__Context7__resolve-library-id", "mcp__Context7__get-library-docs", "mcp__basic-memory__read_note", "<PERSON><PERSON>(docker exec:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(mkdir:*)", "mcp__filesystem__write_file", "mcp__filesystem__get_file_info", "mcp__filesystem__create_directory", "Bash(./build.sh:*)", "Bash(npm run build:*)", "Bash(npm run type-check:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "mcp__playwright__browser_take_screenshot", "mcp__playwright__browser_network_requests", "mcp__playwright__browser_press_key", "mcp__playwright__browser_close", "<PERSON><PERSON>(curl:*)", "mcp__filesystem__edit_file", "<PERSON><PERSON>(chmod:*)", "mcp__playwright__browser_snapshot", "Bash(node:*)", "Bash(search_term=\"io.*transport\\|socket.*emit\\|Socket\\.IO\\|engine.*io\")", "<PERSON><PERSON>(cat:*)", "Bash(npm run lint)", "Bash(npm run:*)", "Bash(npm test)", "Bash(npm test:*)", "mcp__playwright__browser_drag", "Bash(for i in {1..20})", "Bash(do echo \"Left arrow press $i\")", "Bash(done)", "mcp__playwright__browser_tab_list", "mcp__playwright__browser_tab_close", "mcp__playwright__browser_tab_select", "mcp__playwright__browser_hover", "WebFetch(domain:github.com)", "Bash(npx vite:*)", "mcp__playwright__browser_install", "mcp__playwright__browser_select_option", "Bash(npx tsc:*)", "mcp__serena__initial_instructions", "mcp__serena__check_onboarding_performed", "mcp__serena__onboarding", "mcp__serena__list_dir", "mcp__serena__read_file", "mcp__serena__search_for_pattern", "mcp__serena__get_symbols_overview", "mcp__serena__find_symbol", "mcp__serena__find_file", "mcp__serena__get_current_config", "mcp__serena__switch_modes", "mcp__serena__write_memory", "mcp__serena__create_text_file", "mcp__serena__think_about_collected_information", "mcp__serena__replace_regex", "mcp__serena__restart_language_server"], "deny": []}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["time", "Context7", "filesystem", "fetch", "code-reasoning", "playwright", "basic-memory", "serena"]}